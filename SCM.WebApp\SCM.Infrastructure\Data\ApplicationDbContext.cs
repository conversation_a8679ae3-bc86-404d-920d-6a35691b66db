using Microsoft.EntityFrameworkCore;
using SCM.Domain.Entities;

namespace SCM.Infrastructure.Data;

public class ApplicationDbContext : DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    // Core entities
    public DbSet<Department> Departments { get; set; } = null!;
    public DbSet<ProductGroup> ProductGroups { get; set; } = null!;
    public DbSet<ProductSubGroup> ProductSubGroups { get; set; } = null!;
    public DbSet<UnitGroup> UnitGroups { get; set; } = null!;
    public DbSet<Unit> Units { get; set; } = null!;
    public DbSet<Brand> Brands { get; set; } = null!;
    public DbSet<Tax> Taxes { get; set; } = null!;
    public DbSet<Product> Products { get; set; } = null!;
    public DbSet<Barcode> Barcodes { get; set; } = null!;

    // Business Structure entities
    public DbSet<Company> Companies { get; set; } = null!;
    public DbSet<Location> Locations { get; set; } = null!;
    public DbSet<Store> Stores { get; set; } = null!;
    public DbSet<CostCenterType> CostCenterTypes { get; set; } = null!;
    public DbSet<CostCenter> CostCenters { get; set; } = null!;
    public DbSet<ProductCostCenterLink> ProductCostCenterLinks { get; set; } = null!;

    // Inventory entities
    public DbSet<StockOnHand> StockOnHand { get; set; } = null!;
    public DbSet<Batch> Batches { get; set; } = null!;
    public DbSet<StockTakeHeader> StockTakeHeaders { get; set; } = null!;
    public DbSet<StockTakeDetail> StockTakeDetails { get; set; } = null!;
    public DbSet<StockTransferHeader> StockTransferHeaders { get; set; } = null!;
    public DbSet<StockTransferDetail> StockTransferDetails { get; set; } = null!;
    public DbSet<StockAdjustmentHeader> StockAdjustmentHeaders { get; set; } = null!;
    public DbSet<StockAdjustmentDetail> StockAdjustmentDetails { get; set; } = null!;
    public DbSet<StockRequestHeader> StockRequestHeaders { get; set; } = null!;
    public DbSet<StockRequestDetail> StockRequestDetails { get; set; } = null!;
    public DbSet<StockTransaction> StockTransactions { get; set; } = null!;

    // Transaction entities
    public DbSet<TransactionType> TransactionTypes { get; set; } = null!;
    public DbSet<TransactionProcess> TransactionProcesses { get; set; } = null!;
    public DbSet<TransactionStageType> TransactionStageTypes { get; set; } = null!;
    public DbSet<TransactionHeader> TransactionHeaders { get; set; } = null!;
    public DbSet<TransactionDetail> TransactionDetails { get; set; } = null!;
    public DbSet<TransactionStage> TransactionStages { get; set; } = null!;
    public DbSet<PurchaseOrder> PurchaseOrders { get; set; } = null!;
    public DbSet<PurchaseOrderDetail> PurchaseOrderDetails { get; set; } = null!;
    public DbSet<GoodsReceiptHeader> GoodsReceiptHeaders { get; set; } = null!;
    public DbSet<GoodsReceiptDetail> GoodsReceiptDetails { get; set; } = null!;

    // Recipe entities
    public DbSet<Recipe> Recipes { get; set; } = null!;
    public DbSet<RecipeIngredient> RecipeIngredients { get; set; } = null!;

    // User entities
    public DbSet<Role> Roles { get; set; } = null!;
    public DbSet<User> Users { get; set; } = null!;
    public DbSet<Permission> Permissions { get; set; } = null!;
    public DbSet<UserPermission> UserPermissions { get; set; } = null!;
    public DbSet<RolePermission> RolePermissions { get; set; } = null!;
    public DbSet<UserCostCenterAccess> UserCostCenterAccesses { get; set; } = null!;
    public DbSet<UserPreference> UserPreferences { get; set; } = null!;
    public DbSet<ApiKey> ApiKeys { get; set; } = null!;

    // Sales entities
    public DbSet<PaymentMethod> PaymentMethods { get; set; } = null!;
    public DbSet<PaymentDetail> PaymentDetails { get; set; } = null!;
    public DbSet<Currency> Currencies { get; set; } = null!;
    public DbSet<Shift> Shifts { get; set; } = null!;
    public DbSet<Customer> Customers { get; set; } = null!;

    // Other entities
    public DbSet<Supplier> Suppliers { get; set; } = null!;
    public DbSet<DocumentStorage> DocumentStorages { get; set; } = null!;
    public DbSet<AuditLog> AuditLogs { get; set; } = null!;
    public DbSet<Notification> Notifications { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure entity relationships and constraints
        ConfigureCore(modelBuilder);
        ConfigureInventory(modelBuilder);
        ConfigureTransactions(modelBuilder);
        ConfigurePurchaseOrders(modelBuilder);
        ConfigureGoodsReceipt(modelBuilder);
        ConfigureUsers(modelBuilder);
        ConfigureSales(modelBuilder);
    }

    private void ConfigureCore(ModelBuilder modelBuilder)
    {
        // Department
        modelBuilder.Entity<Department>(entity =>
        {
            entity.ToTable("Department");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("DepartmentId");
            entity.Property(e => e.Name).HasMaxLength(150).IsRequired();
            entity.HasIndex(d => d.Name).IsUnique();
        });

        // ProductGroup
        modelBuilder.Entity<ProductGroup>(entity =>
        {
            entity.ToTable("ProductGroup");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("GroupId");
            entity.Property(e => e.Name).HasMaxLength(150).IsRequired();
            entity.Property(e => e.DepartmentId).HasColumnName("DepartmentId");
            entity.HasIndex(g => g.Name).IsUnique();
        });

        // ProductSubGroup
        modelBuilder.Entity<ProductSubGroup>(entity =>
        {
            entity.ToTable("ProductSubGroup");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("SubGroupId");
            entity.Property(e => e.Name).HasMaxLength(150).IsRequired();
            entity.Property(e => e.GroupId).HasColumnName("GroupId");
            entity.HasIndex(sg => sg.Name).IsUnique();
        });

        // UnitGroup
        modelBuilder.Entity<UnitGroup>(entity =>
        {
            entity.ToTable("UnitGroup");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("UnitGroupId");
            entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
            entity.HasIndex(ug => ug.Name).IsUnique();
        });

        // Unit
        modelBuilder.Entity<Unit>(entity =>
        {
            entity.ToTable("Unit");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("UnitId");
            entity.Property(e => e.Name).HasMaxLength(50).IsRequired();
            entity.Property(e => e.Abbreviation).HasMaxLength(10);
            entity.Property(e => e.UnitGroupId).HasColumnName("UnitGroupId");
            entity.Property(e => e.BaseConversionFactor).HasColumnType("decimal(18, 6)");
            entity.Property(e => e.ConversionFactor).HasColumnType("decimal(18, 6)");
            entity.HasIndex(u => u.Name).IsUnique();
        });

        // Brand
        modelBuilder.Entity<Brand>(entity =>
        {
            entity.ToTable("Brand");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("BrandId");
            entity.Property(e => e.Name).HasMaxLength(150).IsRequired();
            entity.HasIndex(b => b.Name).IsUnique();
        });

        // Tax
        modelBuilder.Entity<Tax>(entity =>
        {
            entity.ToTable("Tax");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("TaxId");
            entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
            entity.Property(e => e.Rate).HasColumnType("decimal(5, 2)");
            entity.HasIndex(t => t.Name).IsUnique();
        });

        // Product
        modelBuilder.Entity<Product>(entity =>
        {
            entity.ToTable("Product");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("ProductId");
            entity.Property(e => e.Code).HasMaxLength(50).IsRequired();
            entity.Property(e => e.Name).HasMaxLength(150).IsRequired();
            entity.Property(e => e.BrandId).HasColumnName("BrandId");
            entity.Property(e => e.UnitId).HasColumnName("UnitId");
            entity.Property(e => e.UnitGroupId).HasColumnName("UnitGroupId");
            entity.Property(e => e.DepartmentId).HasColumnName("DepartmentId");
            entity.Property(e => e.GroupId).HasColumnName("GroupId");
            entity.Property(e => e.SubGroupId).HasColumnName("SubGroupId");
            entity.Property(e => e.TaxId).HasColumnName("TaxId");
            entity.Property(e => e.SalesUnitId).HasColumnName("SalesUnitId");
            entity.HasIndex(p => p.Code).IsUnique();
        });

        // Barcode
        modelBuilder.Entity<Barcode>(entity =>
        {
            entity.ToTable("Barcode");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("BarcodeId");
            entity.Property(e => e.ProductId).HasColumnName("ProductId").IsRequired();
            entity.Property(e => e.BarcodeValue).HasMaxLength(100).IsRequired();
            entity.Property(e => e.UnitId).HasColumnName("UnitId");
            entity.HasIndex(b => b.BarcodeValue).IsUnique();
        });
    }

    private void ConfigureInventory(ModelBuilder modelBuilder)
    {
        // Company
        modelBuilder.Entity<Company>(entity =>
        {
            entity.ToTable("Company");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("CompanyId");
            entity.Property(e => e.Name).HasMaxLength(150).IsRequired();
            entity.Property(e => e.Code).HasMaxLength(50);
            entity.HasIndex(c => c.Name).IsUnique();
        });

        // Store
        modelBuilder.Entity<Store>(entity =>
        {
            entity.ToTable("Store");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("StoreId");
            entity.Property(e => e.Name).HasMaxLength(150).IsRequired();
            entity.Property(e => e.LocationId).HasColumnName("LocationId");
            entity.Property(e => e.IsSalesPoint).HasColumnName("IsSalesPoint");
            entity.Property(e => e.LogoPath).HasColumnName("LogoPath");
            entity.HasIndex(s => s.Name).IsUnique();
        });

        // Location
        modelBuilder.Entity<Location>(entity =>
        {
            entity.ToTable("Location");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("LocationId");
            entity.Property(e => e.Name).HasMaxLength(150).IsRequired();
            entity.Property(e => e.CompanyId).HasColumnName("CompanyId");
            entity.HasIndex(l => l.Name).IsUnique();
        });

        // CostCenterType
        modelBuilder.Entity<CostCenterType>(entity =>
        {
            entity.ToTable("CostCenterType");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("TypeId");
            entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
            entity.HasIndex(ct => ct.Name).IsUnique();
        });

        // CostCenter
        modelBuilder.Entity<CostCenter>(entity =>
        {
            entity.ToTable("CostCenter");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("CostCenterId");
            entity.Property(e => e.Name).HasMaxLength(150).IsRequired();
            entity.Property(e => e.StoreId).HasColumnName("StoreId");
            entity.Property(e => e.TypeId).HasColumnName("TypeId");
            entity.Property(e => e.TypeName).HasMaxLength(100);
            entity.Property(e => e.Abbreviation).HasMaxLength(50);
            entity.HasIndex(cc => cc.Name).IsUnique();

            // Ignore properties that don't exist in the database
            entity.Ignore(e => e.Code);
            entity.Ignore(e => e.Description);
        });

        // ProductCostCenterLink
        modelBuilder.Entity<ProductCostCenterLink>(entity =>
        {
            entity.ToTable("ProductCostCenterLink");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("LinkId");
            entity.Property(e => e.ProductId).HasColumnName("ProductId").IsRequired();
            entity.Property(e => e.CostCenterId).HasColumnName("CostCenterId").IsRequired();
            entity.Property(e => e.MinStock).HasColumnName("MinimumStock").HasColumnType("decimal(18, 4)");
            entity.Property(e => e.MaxStock).HasColumnName("MaximumStock").HasColumnType("decimal(18, 4)");
            entity.HasIndex(pcl => new { pcl.ProductId, pcl.CostCenterId }).IsUnique();
        });

        // Batch
        modelBuilder.Entity<Batch>(entity =>
        {
            entity.ToTable("Batch");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("BatchId");
            entity.Property(e => e.BatchNumber).HasMaxLength(150).IsRequired();
            entity.Property(e => e.ProductId).HasColumnName("ProductId").IsRequired();
            entity.Property(e => e.UnitId).HasColumnName("UnitId");
            entity.HasIndex(b => b.BatchNumber);
        });

        // StockOnHand
        modelBuilder.Entity<StockOnHand>(entity =>
        {
            entity.ToTable("StockOnHand");
            entity.HasKey(s => s.StockId);
            entity.Property(e => e.StockId).HasColumnName("StockId").IsRequired();
            entity.Property(e => e.ProductId).HasColumnName("ProductId").IsRequired();
            entity.Property(e => e.CostCenterId).HasColumnName("CostCenterId").IsRequired();
            entity.Property(e => e.UnitId).HasColumnName("UnitId");
            entity.Property(e => e.Quantity).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.BaseQuantity).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.AverageCost).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.CostPrice).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.ReturnVariance).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.NetCost).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.SalesPrice).HasColumnType("decimal(18, 4)");

            // Explicitly ignore any BatchId property that might be inferred
            entity.Ignore("BatchId");
        });

        // StockTransaction
        modelBuilder.Entity<StockTransaction>(entity =>
        {
            entity.ToTable("StockTransaction");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("StockTransactionId");
            entity.Property(e => e.ProductId).HasColumnName("ProductId").IsRequired();
            entity.Property(e => e.CostCenterId).HasColumnName("CostCenterId").IsRequired();
            entity.Property(e => e.BatchId).HasColumnName("BatchId").IsRequired();
            entity.Property(e => e.UnitId).HasColumnName("UnitId").IsRequired();
            entity.Property(e => e.Quantity).HasColumnType("decimal(18, 4)").IsRequired();
            entity.Property(e => e.TransactionDate).HasColumnType("datetime2").IsRequired();
            entity.Property(e => e.Source).HasMaxLength(100).IsRequired();
            entity.Property(e => e.TransactionId).HasColumnName("TransactionId");

            // Configure relationships
            entity.HasOne(st => st.Product)
                .WithMany()
                .HasForeignKey(st => st.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(st => st.CostCenter)
                .WithMany()
                .HasForeignKey(st => st.CostCenterId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(st => st.Batch)
                .WithMany()
                .HasForeignKey(st => st.BatchId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(st => st.Unit)
                .WithMany()
                .HasForeignKey(st => st.UnitId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(st => st.Transaction)
                .WithMany()
                .HasForeignKey(st => st.TransactionId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // StockTakeHeader
        modelBuilder.Entity<StockTakeHeader>(entity =>
        {
            entity.ToTable("StockTakeHeader");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("StockTakeId");
            entity.Property(e => e.ReferenceNumber).HasColumnName("StockTakeNumber").HasMaxLength(50).IsRequired();
            entity.Property(e => e.CostCenterId).HasColumnName("CostCenterId").IsRequired();
            entity.Property(e => e.StockTakeDate).HasColumnType("datetime2");
            entity.Property(e => e.Status).HasMaxLength(20);
            entity.Property(e => e.CreatedById).HasColumnName("CreatedById");
            entity.Property(e => e.CompletedById).HasColumnName("CompletedById");
            entity.Property(e => e.CompletedAt).HasColumnName("CompletedDate");
            entity.HasIndex(sth => sth.ReferenceNumber).IsUnique();

            // Configure relationships with User
            entity.HasOne(sth => sth.CreatedBy)
                .WithMany()
                .HasForeignKey(sth => sth.CreatedById)
                .HasConstraintName("FK_StockTakeHeader_User_CreatedById")
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(sth => sth.CompletedBy)
                .WithMany()
                .HasForeignKey(sth => sth.CompletedById)
                .HasConstraintName("FK_StockTakeHeader_User_CompletedById")
                .OnDelete(DeleteBehavior.Restrict);
        });

        // StockTakeDetail
        modelBuilder.Entity<StockTakeDetail>(entity =>
        {
            entity.ToTable("StockTakeDetail");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("StockTakeDetailId");
            entity.Property(e => e.StockTakeHeaderId).HasColumnName("StockTakeId").IsRequired();
            entity.Property(e => e.ProductId).HasColumnName("ProductId").IsRequired();
            entity.Property(e => e.BatchId).HasColumnName("BatchId");
            entity.Property(e => e.UnitId).HasColumnName("UnitId").IsRequired();
            entity.Property(e => e.SystemQuantity).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.CountedQuantity).HasColumnName("ActualQuantity").HasColumnType("decimal(18, 4)");
            entity.Property(e => e.Variance).HasColumnName("VarianceQuantity").HasColumnType("decimal(18, 4)");
        });

        // StockTransferHeader
        modelBuilder.Entity<StockTransferHeader>(entity =>
        {
            entity.ToTable("StockTransferHeader");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("StockTransferId");
            entity.Property(e => e.ReferenceNumber).HasColumnName("ReferenceNumber").HasMaxLength(50).IsRequired();
            entity.Property(e => e.FromCostCenterId).HasColumnName("FromCostCenterId").IsRequired();
            entity.Property(e => e.ToCostCenterId).HasColumnName("ToCostCenterId").IsRequired();
            entity.Property(e => e.TransferDate).HasColumnType("datetime2");
            entity.Property(e => e.Status).HasMaxLength(20);
            entity.Property(e => e.CreatedById).HasColumnName("CreatedById");
            entity.Property(e => e.CompletedById).HasColumnName("CompletedById");
            entity.Property(e => e.CompletedAt).HasColumnName("CompletedDate");
            entity.HasIndex(sth => sth.ReferenceNumber).IsUnique();

            // Configure relationships with CostCenter
            entity.HasOne(sth => sth.FromCostCenter)
                .WithMany()
                .HasForeignKey(sth => sth.FromCostCenterId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(sth => sth.ToCostCenter)
                .WithMany()
                .HasForeignKey(sth => sth.ToCostCenterId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure relationships with User
            entity.HasOne(sth => sth.CreatedBy)
                .WithMany()
                .HasForeignKey(sth => sth.CreatedById)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(sth => sth.CompletedBy)
                .WithMany()
                .HasForeignKey(sth => sth.CompletedById)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // StockTransferDetail
        modelBuilder.Entity<StockTransferDetail>(entity =>
        {
            entity.ToTable("StockTransferDetail");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("StockTransferDetailId");
            entity.Property(e => e.StockTransferHeaderId).HasColumnName("StockTransferId").IsRequired();
            entity.Property(e => e.ProductId).HasColumnName("ProductId").IsRequired();
            entity.Property(e => e.BatchId).HasColumnName("BatchId");
            entity.Property(e => e.UnitId).HasColumnName("UnitId");
            entity.Property(e => e.Quantity).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.CostPrice).HasColumnType("decimal(18, 4)");

            // Configure relationships
            entity.HasOne(std => std.StockTransferHeader)
                .WithMany(sth => sth.Details)
                .HasForeignKey(std => std.StockTransferHeaderId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(std => std.Product)
                .WithMany()
                .HasForeignKey(std => std.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(std => std.Batch)
                .WithMany()
                .HasForeignKey(std => std.BatchId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(std => std.Unit)
                .WithMany()
                .HasForeignKey(std => std.UnitId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // StockAdjustmentHeader
        modelBuilder.Entity<StockAdjustmentHeader>(entity =>
        {
            entity.ToTable("StockAdjustmentHeader");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("StockAdjustmentId");
            entity.Property(e => e.ReferenceNumber).HasColumnName("ReferenceNumber").HasMaxLength(50).IsRequired();
            entity.Property(e => e.CostCenterId).HasColumnName("CostCenterId").IsRequired();
            entity.Property(e => e.AdjustmentDate).HasColumnType("datetime2");
            entity.Property(e => e.Status).HasMaxLength(20);
            entity.Property(e => e.CreatedById).HasColumnName("CreatedById");
            entity.Property(e => e.CompletedById).HasColumnName("CompletedById");
            entity.Property(e => e.CompletedAt).HasColumnName("CompletedDate");
            entity.HasIndex(sah => sah.ReferenceNumber).IsUnique();

            // Configure relationships with CostCenter
            entity.HasOne(sah => sah.CostCenter)
                .WithMany()
                .HasForeignKey(sah => sah.CostCenterId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure relationships with User
            entity.HasOne(sah => sah.CreatedBy)
                .WithMany()
                .HasForeignKey(sah => sah.CreatedById)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(sah => sah.CompletedBy)
                .WithMany()
                .HasForeignKey(sah => sah.CompletedById)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // StockAdjustmentDetail
        modelBuilder.Entity<StockAdjustmentDetail>(entity =>
        {
            entity.ToTable("StockAdjustmentDetail");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("StockAdjustmentDetailId");
            entity.Property(e => e.StockAdjustmentHeaderId).HasColumnName("StockAdjustmentId").IsRequired();
            entity.Property(e => e.ProductId).HasColumnName("ProductId").IsRequired();
            entity.Property(e => e.BatchId).HasColumnName("BatchId");
            entity.Property(e => e.UnitId).HasColumnName("UnitId");
            entity.Property(e => e.CurrentQuantity).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.AdjustmentQuantity).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.NewQuantity).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.CostPrice).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.Reason).HasMaxLength(100).IsRequired();

            // Configure relationships
            entity.HasOne(sad => sad.StockAdjustmentHeader)
                .WithMany(sah => sah.Details)
                .HasForeignKey(sad => sad.StockAdjustmentHeaderId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(sad => sad.Product)
                .WithMany()
                .HasForeignKey(sad => sad.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(sad => sad.Batch)
                .WithMany()
                .HasForeignKey(sad => sad.BatchId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(sad => sad.Unit)
                .WithMany()
                .HasForeignKey(sad => sad.UnitId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // StockRequestHeader
        modelBuilder.Entity<StockRequestHeader>(entity =>
        {
            entity.ToTable("StockRequestHeader");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("StockRequestId");
            entity.Property(e => e.ReferenceNumber).HasColumnName("ReferenceNumber").HasMaxLength(50).IsRequired();
            entity.Property(e => e.FromCostCenterId).HasColumnName("FromCostCenterId").IsRequired();
            entity.Property(e => e.ToCostCenterId).HasColumnName("ToCostCenterId").IsRequired();
            entity.Property(e => e.RequestDate).HasColumnType("datetime2");
            entity.Property(e => e.Status).HasMaxLength(20);
            entity.Property(e => e.CreatedById).HasColumnName("CreatedById");
            entity.Property(e => e.ApprovedById).HasColumnName("ApprovedById");
            entity.Property(e => e.ApprovedAt).HasColumnName("ApprovedDate");
            entity.Property(e => e.CompletedById).HasColumnName("CompletedById");
            entity.Property(e => e.CompletedAt).HasColumnName("CompletedDate");
            entity.HasIndex(srh => srh.ReferenceNumber).IsUnique();

            // Configure relationships with CostCenter
            entity.HasOne(srh => srh.FromCostCenter)
                .WithMany()
                .HasForeignKey(srh => srh.FromCostCenterId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(srh => srh.ToCostCenter)
                .WithMany()
                .HasForeignKey(srh => srh.ToCostCenterId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure relationships with User
            entity.HasOne(srh => srh.CreatedBy)
                .WithMany()
                .HasForeignKey(srh => srh.CreatedById)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(srh => srh.ApprovedBy)
                .WithMany()
                .HasForeignKey(srh => srh.ApprovedById)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(srh => srh.CompletedBy)
                .WithMany()
                .HasForeignKey(srh => srh.CompletedById)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // StockRequestDetail
        modelBuilder.Entity<StockRequestDetail>(entity =>
        {
            entity.ToTable("StockRequestDetail");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("StockRequestDetailId");
            entity.Property(e => e.StockRequestHeaderId).HasColumnName("StockRequestId").IsRequired();
            entity.Property(e => e.ProductId).HasColumnName("ProductId").IsRequired();
            entity.Property(e => e.BatchId).HasColumnName("BatchId");
            entity.Property(e => e.UnitId).HasColumnName("UnitId");
            entity.Property(e => e.Quantity).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.Price).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.DeliveryDate).HasColumnType("datetime2");

            // Configure relationships
            entity.HasOne(srd => srd.StockRequestHeader)
                .WithMany(srh => srh.Details)
                .HasForeignKey(srd => srd.StockRequestHeaderId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(srd => srd.Product)
                .WithMany()
                .HasForeignKey(srd => srd.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(srd => srd.Batch)
                .WithMany()
                .HasForeignKey(srd => srd.BatchId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(srd => srd.Unit)
                .WithMany()
                .HasForeignKey(srd => srd.UnitId)
                .OnDelete(DeleteBehavior.Restrict);
        });
    }

    private void ConfigureTransactions(ModelBuilder modelBuilder)
    {
        // TransactionType
        modelBuilder.Entity<TransactionType>(entity =>
        {
            entity.ToTable("TransactionType");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("TransactionTypeId");
            entity.Property(e => e.Name).HasMaxLength(50).IsRequired();
            entity.Property(e => e.AffectsInventory).HasColumnName("AffectsInventory");
            entity.HasIndex(tt => tt.Name).IsUnique();

            // Ignore navigation properties to avoid shadow properties
            entity.Ignore(e => e.Processes);
            entity.Ignore(e => e.TransactionHeaders);
        });

        // TransactionProcess
        modelBuilder.Entity<TransactionProcess>(entity =>
        {
            entity.ToTable("TransactionProcess");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("ProcessId");
            entity.Property(e => e.ProcessNumber).HasColumnName("ProcessNumber").HasMaxLength(50).IsRequired();
            // Don't map Name to a database column, it will be a computed property
            entity.Ignore(e => e.Name);
            entity.HasIndex(tp => tp.ProcessNumber).IsUnique();

            // Ignore navigation properties to avoid shadow properties
            entity.Ignore(e => e.TransactionHeaders);

            // Configure relationships with User
            entity.HasOne(tp => tp.CreatedBy)
                .WithMany()
                .HasForeignKey(tp => tp.CreatedById)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // TransactionStageType
        modelBuilder.Entity<TransactionStageType>(entity =>
        {
            entity.ToTable("TransactionStageType");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("StageTypeId");
            entity.Property(e => e.Name).HasMaxLength(50).IsRequired();
            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.Sequence).HasColumnName("Sequence");
            entity.Property(e => e.CreatedAt).HasColumnName("CreatedAt");
            entity.Property(e => e.UpdatedAt).HasColumnName("UpdatedAt");
            entity.Property(e => e.IsActive).HasColumnName("IsActive");
            entity.HasIndex(tst => tst.Name).IsUnique();

            // Ignore navigation properties to avoid shadow properties
            entity.Ignore(e => e.TransactionStages);
            entity.Ignore(e => e.TransactionHeaders);
        });

        // TransactionHeader
        modelBuilder.Entity<TransactionHeader>(entity =>
        {
            entity.ToTable("TransactionHeader");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("TransactionId");
            entity.Property(e => e.TransactionNumber).HasColumnName("TransactionNumber").HasMaxLength(50).IsRequired();
            entity.Property(e => e.ProcessId).HasColumnName("ProcessId");
            entity.Property(e => e.StageTypeId).HasColumnName("StageTypeId");
            entity.Property(e => e.TransactionTypeId).HasColumnName("TransactionTypeId");
            entity.Property(e => e.SourceCostCenterId).HasColumnName("SourceCostCenterId");
            entity.Property(e => e.DestinationCostCenterId).HasColumnName("DestinationCostCenterId");
            entity.Property(e => e.SupplierId).HasColumnName("SupplierId");
            entity.Property(e => e.ReferenceNumber).HasColumnName("ReferenceNumber").HasMaxLength(50);
            entity.Property(e => e.TransactionDate).HasColumnName("TransactionDate");
            entity.Property(e => e.Notes).HasColumnName("Notes");
            entity.Property(e => e.SubTotal).HasColumnName("SubTotal");
            entity.Property(e => e.TaxAmount).HasColumnName("TaxAmount");
            entity.Property(e => e.TotalAmount).HasColumnName("TotalAmount");
            entity.Property(e => e.DiscountAmount).HasColumnName("DiscountAmount");
            entity.Property(e => e.DiscountPercentage).HasColumnName("DiscountPercentage");
            entity.Property(e => e.Status).HasColumnName("Status").HasMaxLength(20);
            entity.Property(e => e.CreatedById).HasColumnName("CreatedById");
            entity.Property(e => e.ApprovedById).HasColumnName("ApprovedById");
            entity.Property(e => e.ApprovedDate).HasColumnName("ApprovedDate");
            entity.Property(e => e.CreatedAt).HasColumnName("CreatedAt");
            entity.Property(e => e.UpdatedAt).HasColumnName("UpdatedAt");
            entity.Property(e => e.IsActive).HasColumnName("IsActive");
            entity.Property(e => e.RelatedTransactionId).HasColumnName("RelatedTransactionId");
            entity.Property(e => e.RelatedTransactionNumber).HasColumnName("RelatedTransactionNumber").HasMaxLength(50);
            entity.Property(e => e.IsSkippedStep).HasColumnName("IsSkippedStep").HasDefaultValue(false);
            entity.HasIndex(th => th.TransactionNumber).IsUnique();

            // Ignore navigation properties to avoid shadow properties (except Details which we need)
            entity.Ignore(e => e.StageType);
            entity.Ignore(e => e.SourceCostCenter);
            entity.Ignore(e => e.DestinationCostCenter);
            entity.Ignore(e => e.Supplier);
            entity.Ignore(e => e.Customer);
            entity.Ignore(e => e.Currency);
            // Keep Details collection - it's properly configured in TransactionDetail configuration

            // Explicitly ignore any shadow properties that don't exist in the database
            entity.Ignore("TransactionProcessId");
            entity.Ignore("TransactionProcessId1");
            entity.Ignore("TransactionProcessId2");
            entity.Ignore("TransactionProcessId3");
            entity.Ignore("TransactionStageTypeId");
            entity.Ignore("TransactionStageTypeId1");
            entity.Ignore("TransactionStageTypeId2");
        });

        // TransactionDetail
        modelBuilder.Entity<TransactionDetail>(entity =>
        {
            entity.ToTable("TransactionDetail");

            // Configure decimal precision for numeric fields
            entity.Property(e => e.Quantity).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.UnitPrice).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.TaxRate).HasColumnType("decimal(5, 2)");
            entity.Property(e => e.TaxAmount).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.DiscountPercentage).HasColumnType("decimal(5, 2)");
            entity.Property(e => e.DiscountAmount).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.LineTotal).HasColumnType("decimal(18, 4)");

            // Configure only the essential relationship
            entity.HasOne(td => td.Transaction)
                .WithMany(th => th.Details)
                .HasForeignKey(td => td.TransactionId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // Supplier
        modelBuilder.Entity<Supplier>(entity =>
        {
            entity.ToTable("Supplier");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("SupplierId");
            entity.Property(e => e.Name).HasMaxLength(150).IsRequired();
            entity.HasIndex(s => s.Name).IsUnique();

            // Ignore navigation properties to avoid shadow properties
            entity.Ignore(e => e.Transactions);
        });
    }

    private void ConfigureUsers(ModelBuilder modelBuilder)
    {
        // Role
        modelBuilder.Entity<Role>(entity =>
        {
            entity.ToTable("Role");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("RoleId");
            entity.Property(e => e.Name).HasMaxLength(50).IsRequired();
            entity.HasIndex(r => r.Name).IsUnique();
        });

        // User
        modelBuilder.Entity<User>(entity =>
        {
            entity.ToTable("User");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("UserId");
            entity.Property(e => e.Username).HasMaxLength(50).IsRequired();
            entity.Property(e => e.Email).HasMaxLength(150);
            entity.Property(e => e.PasswordHash).HasMaxLength(500).IsRequired();
            entity.Property(e => e.FirstName).HasMaxLength(100);
            entity.Property(e => e.LastName).HasMaxLength(100);
            entity.Property(e => e.Phone).HasColumnName("PhoneNumber").HasMaxLength(50);
            entity.Property(e => e.RoleId).HasColumnName("RoleId");
            entity.Property(e => e.LastLogin).HasColumnName("LastLoginDate");

            // Ignore properties that don't exist in the database
            entity.Ignore(e => e.IsAdmin);
            entity.Ignore(e => e.IsLocked);
            entity.Ignore(e => e.MustChangePassword);
            entity.Ignore(e => e.FailedLoginAttempts);
            entity.HasIndex(u => u.Username).IsUnique();
            entity.HasIndex(u => u.Email).IsUnique();

            // Configure User-Role relationship
            entity.HasOne(u => u.Role)
                .WithMany(r => r.Users)
                .HasForeignKey(u => u.RoleId)
                .OnDelete(DeleteBehavior.SetNull);
        });

        // UserCostCenterAccess
        modelBuilder.Entity<UserCostCenterAccess>(entity =>
        {
            entity.ToTable("UserCostCenterAccess");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("AccessId");
            entity.Property(e => e.UserId).HasColumnName("UserId").IsRequired();
            entity.Property(e => e.CostCenterId).HasColumnName("CostCenterId").IsRequired();
            entity.HasIndex(ucca => new { ucca.UserId, ucca.CostCenterId }).IsUnique();
        });

        // Permission
        modelBuilder.Entity<Permission>(entity =>
        {
            entity.ToTable("Permission");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("PermissionId");
            entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.Category).HasMaxLength(50).IsRequired();
            entity.HasIndex(p => p.Name).IsUnique();
        });

        // UserPermission
        modelBuilder.Entity<UserPermission>(entity =>
        {
            entity.ToTable("UserPermission");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("UserPermissionId");
            entity.Property(e => e.UserId).HasColumnName("UserId").IsRequired();
            entity.Property(e => e.PermissionId).HasColumnName("PermissionId").IsRequired();
            entity.HasIndex(up => new { up.UserId, up.PermissionId }).IsUnique();

            // Configure relationships
            entity.HasOne(up => up.User)
                .WithMany(u => u.UserPermissions)
                .HasForeignKey(up => up.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(up => up.Permission)
                .WithMany(p => p.UserPermissions)
                .HasForeignKey(up => up.PermissionId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // RolePermission
        modelBuilder.Entity<RolePermission>(entity =>
        {
            entity.ToTable("RolePermission");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("RolePermissionId");
            entity.Property(e => e.RoleId).HasColumnName("RoleId").IsRequired();
            entity.Property(e => e.PermissionId).HasColumnName("PermissionId").IsRequired();
            entity.HasIndex(rp => new { rp.RoleId, rp.PermissionId }).IsUnique();

            // Configure relationships
            entity.HasOne(rp => rp.Role)
                .WithMany()
                .HasForeignKey(rp => rp.RoleId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(rp => rp.Permission)
                .WithMany(p => p.RolePermissions)
                .HasForeignKey(rp => rp.PermissionId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // UserPreference
        modelBuilder.Entity<UserPreference>(entity =>
        {
            entity.ToTable("UserPreference");
            entity.HasKey(e => e.PreferenceId);
            entity.Property(e => e.PreferenceId).HasColumnName("PreferenceId");
            entity.Property(e => e.UserId).HasColumnName("UserId").IsRequired();
            entity.Property(e => e.PreferenceKey).HasMaxLength(100).IsRequired();
            entity.Property(e => e.PreferenceValue);
            entity.HasIndex(up => new { up.UserId, up.PreferenceKey }).IsUnique();
        });

        // ApiKey
        modelBuilder.Entity<ApiKey>(entity =>
        {
            entity.ToTable("ApiKey");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("ApiKeyId");
            entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
            entity.Property(e => e.KeyValue).HasMaxLength(100).IsRequired();
            entity.Property(e => e.CreatedById).HasColumnName("CreatedById").IsRequired();
            entity.HasIndex(ak => ak.KeyValue).IsUnique();

            // Configure relationship with User
            entity.HasOne(ak => ak.CreatedBy)
                .WithMany()
                .HasForeignKey(ak => ak.CreatedById)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // Notification
        modelBuilder.Entity<Notification>(entity =>
        {
            entity.ToTable("Notification");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("NotificationId");
            entity.Property(e => e.UserId).HasColumnName("UserId").IsRequired();
            entity.Property(e => e.Title).HasMaxLength(150).IsRequired();
            entity.Property(e => e.NotificationType).HasMaxLength(50);

            // Configure relationship with User
            entity.HasOne(n => n.User)
                .WithMany()
                .HasForeignKey(n => n.UserId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // AuditLog
        modelBuilder.Entity<AuditLog>(entity =>
        {
            entity.ToTable("AuditLog");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("AuditLogId");
            entity.Property(e => e.UserId).HasColumnName("UserId");
            entity.Property(e => e.Action).HasMaxLength(50).IsRequired();
            entity.Property(e => e.TableName).HasMaxLength(100).IsRequired();
            entity.Property(e => e.RecordId).HasMaxLength(50).IsRequired();
            entity.Property(e => e.IpAddress).HasMaxLength(50);

            // Configure relationship with User
            entity.HasOne(al => al.User)
                .WithMany()
                .HasForeignKey(al => al.UserId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // DocumentStorage
        modelBuilder.Entity<DocumentStorage>(entity =>
        {
            entity.ToTable("DocumentStorage");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("DocumentId");
            entity.Property(e => e.FileName).HasMaxLength(255).IsRequired();
            entity.Property(e => e.FileType).HasMaxLength(100).IsRequired();
            entity.Property(e => e.FileSize).HasColumnType("bigint").IsRequired();
            entity.Property(e => e.FilePath).HasMaxLength(500).IsRequired();
            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.UploadedById).HasColumnName("UploadedById").IsRequired();

            // Configure relationship with User
            entity.HasOne(ds => ds.UploadedBy)
                .WithMany()
                .HasForeignKey(ds => ds.UploadedById)
                .OnDelete(DeleteBehavior.Restrict);
        });
    }

    private void ConfigurePurchaseOrders(ModelBuilder modelBuilder)
    {
        // PurchaseOrder
        modelBuilder.Entity<PurchaseOrder>(entity =>
        {
            entity.ToTable("PurchaseOrder");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("PurchaseOrderId");
            entity.Property(e => e.DocumentNumber).HasMaxLength(50).IsRequired();
            entity.Property(e => e.SupplierId).HasColumnName("SupplierId").IsRequired();
            entity.Property(e => e.CostCenterId).HasColumnName("CostCenterId").IsRequired();
            entity.Property(e => e.OrderDate).HasColumnType("datetime2").IsRequired();
            entity.Property(e => e.ExpectedDeliveryDate).HasColumnType("datetime2");
            entity.Property(e => e.Status).HasMaxLength(20).IsRequired();
            entity.Property(e => e.Notes).HasMaxLength(500);
            entity.Property(e => e.TotalAmount).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.CreatedBy).HasMaxLength(100);
            entity.Property(e => e.CreatedAt).HasColumnType("datetime2").IsRequired();
            entity.Property(e => e.UpdatedBy).HasMaxLength(100);
            entity.Property(e => e.UpdatedAt).HasColumnType("datetime2");
            entity.HasIndex(po => po.DocumentNumber).IsUnique();

            // Relationships
            entity.HasOne(po => po.Supplier)
                .WithMany()
                .HasForeignKey(po => po.SupplierId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(po => po.CostCenter)
                .WithMany()
                .HasForeignKey(po => po.CostCenterId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // PurchaseOrderDetail
        modelBuilder.Entity<PurchaseOrderDetail>(entity =>
        {
            entity.ToTable("PurchaseOrderDetail");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("PurchaseOrderDetailId");
            entity.Property(e => e.PurchaseOrderId).HasColumnName("PurchaseOrderId").IsRequired();
            entity.Property(e => e.ProductId).HasColumnName("ProductId").IsRequired();
            entity.Property(e => e.Quantity).HasColumnType("decimal(18, 4)").IsRequired();
            entity.Property(e => e.UnitPrice).HasColumnType("decimal(18, 4)").IsRequired();
            entity.Property(e => e.TotalPrice).HasColumnType("decimal(18, 4)").IsRequired();
            entity.Property(e => e.Notes).HasMaxLength(500);

            // Relationships
            entity.HasOne(pod => pod.PurchaseOrder)
                .WithMany(po => po.Details)
                .HasForeignKey(pod => pod.PurchaseOrderId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(pod => pod.Product)
                .WithMany()
                .HasForeignKey(pod => pod.ProductId)
                .OnDelete(DeleteBehavior.Restrict);
        });
    }

    private void ConfigureGoodsReceipt(ModelBuilder modelBuilder)
    {
        // GoodsReceiptHeader
        modelBuilder.Entity<GoodsReceiptHeader>(entity =>
        {
            entity.ToTable("GoodsReceiptHeader");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("GoodsReceiptId");
            entity.Property(e => e.DocumentNumber).HasMaxLength(50).IsRequired();
            entity.Property(e => e.PurchaseOrderId).HasColumnName("PurchaseOrderId");
            entity.Property(e => e.SupplierId).HasColumnName("SupplierId").IsRequired();
            entity.Property(e => e.CostCenterId).HasColumnName("CostCenterId").IsRequired();
            entity.Property(e => e.ReceiptDate).HasColumnType("datetime2").IsRequired();
            entity.Property(e => e.DeliveryNoteNumber).HasMaxLength(50);
            entity.Property(e => e.InvoiceNumber).HasMaxLength(50);
            entity.Property(e => e.InvoiceDate).HasColumnType("datetime2");
            entity.Property(e => e.Status).HasMaxLength(20).IsRequired();
            entity.Property(e => e.Notes).HasMaxLength(500);
            entity.Property(e => e.TotalAmount).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.ReceivedById).HasColumnName("ReceivedById");
            entity.Property(e => e.ApprovedById).HasColumnName("ApprovedById");
            entity.Property(e => e.ApprovedAt).HasColumnType("datetime2");
            entity.HasIndex(gr => gr.DocumentNumber).IsUnique();

            // Relationships
            entity.HasOne(gr => gr.PurchaseOrder)
                .WithMany()
                .HasForeignKey(gr => gr.PurchaseOrderId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(gr => gr.Supplier)
                .WithMany()
                .HasForeignKey(gr => gr.SupplierId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(gr => gr.CostCenter)
                .WithMany()
                .HasForeignKey(gr => gr.CostCenterId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(gr => gr.ReceivedBy)
                .WithMany()
                .HasForeignKey(gr => gr.ReceivedById)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(gr => gr.ApprovedBy)
                .WithMany()
                .HasForeignKey(gr => gr.ApprovedById)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // GoodsReceiptDetail
        modelBuilder.Entity<GoodsReceiptDetail>(entity =>
        {
            entity.ToTable("GoodsReceiptDetail");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("GoodsReceiptDetailId");
            entity.Property(e => e.GoodsReceiptHeaderId).HasColumnName("GoodsReceiptId").IsRequired();
            entity.Property(e => e.ProductId).HasColumnName("ProductId").IsRequired();
            entity.Property(e => e.PurchaseOrderDetailId).HasColumnName("PurchaseOrderDetailId");
            entity.Property(e => e.BatchId).HasColumnName("BatchId");
            entity.Property(e => e.UnitId).HasColumnName("UnitId");
            entity.Property(e => e.OrderedQuantity).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.ReceivedQuantity).HasColumnType("decimal(18, 4)").IsRequired();
            entity.Property(e => e.UnitPrice).HasColumnType("decimal(18, 4)").IsRequired();
            entity.Property(e => e.TotalPrice).HasColumnType("decimal(18, 4)").IsRequired();
            entity.Property(e => e.ExpiryDate).HasColumnType("datetime2");
            entity.Property(e => e.Notes).HasMaxLength(500);

            // Relationships
            entity.HasOne(grd => grd.GoodsReceiptHeader)
                .WithMany(grh => grh.Details)
                .HasForeignKey(grd => grd.GoodsReceiptHeaderId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(grd => grd.Product)
                .WithMany()
                .HasForeignKey(grd => grd.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(grd => grd.PurchaseOrderDetail)
                .WithMany()
                .HasForeignKey(grd => grd.PurchaseOrderDetailId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(grd => grd.Batch)
                .WithMany()
                .HasForeignKey(grd => grd.BatchId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(grd => grd.Unit)
                .WithMany()
                .HasForeignKey(grd => grd.UnitId)
                .OnDelete(DeleteBehavior.Restrict);
        });
    }



    private void ConfigureSales(ModelBuilder modelBuilder)
    {
        // PaymentMethod
        modelBuilder.Entity<PaymentMethod>(entity =>
        {
            entity.ToTable("PaymentMethod");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("PaymentMethodId");
            entity.Property(e => e.Name).HasMaxLength(150).IsRequired();
            entity.HasIndex(pm => pm.Name).IsUnique();
        });

        // PaymentDetail
        modelBuilder.Entity<PaymentDetail>(entity =>
        {
            entity.ToTable("Payment");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("PaymentId");
            entity.Property(e => e.TransactionHeaderId).HasColumnName("TransactionId").IsRequired();
            entity.Property(e => e.PaymentMethodId).HasColumnName("PaymentMethodId").IsRequired();
            entity.Property(e => e.Amount).HasColumnType("decimal(18, 4)").IsRequired();
            entity.Property(e => e.PaymentDate).HasDefaultValueSql("GETDATE()");
        });

        // Currency
        modelBuilder.Entity<Currency>(entity =>
        {
            entity.ToTable("Currency");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("CurrencyId");
            entity.Property(e => e.Code).HasMaxLength(3).IsRequired();
            entity.Property(e => e.Name).HasMaxLength(50).IsRequired();
            entity.Property(e => e.Symbol).HasMaxLength(5);
            entity.Property(e => e.ExchangeRate).HasColumnType("decimal(18, 6)");
            entity.Property(e => e.IsDefault).HasColumnName("IsBaseCurrency");
            entity.HasIndex(c => c.Code).IsUnique();

            // Ignore navigation properties to avoid shadow properties
            entity.Ignore(e => e.Transactions);
        });

        // Shift
        modelBuilder.Entity<Shift>(entity =>
        {
            entity.ToTable("Shift");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("ShiftId");
            entity.Property(e => e.Name).HasMaxLength(150).IsRequired();
            entity.Property(e => e.StartTime).HasColumnType("time");
            entity.Property(e => e.EndTime).HasColumnType("time");
            entity.HasIndex(s => s.Name).IsUnique();
        });

        // Customer
        modelBuilder.Entity<Customer>(entity =>
        {
            entity.ToTable("Customer");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("CustomerId");
            entity.Property(e => e.CustomerCode).HasMaxLength(50);
            entity.Property(e => e.FirstName).HasMaxLength(100).IsRequired();
            entity.Property(e => e.LastName).HasMaxLength(100);
            entity.Property(e => e.CompanyName).HasMaxLength(150);
            entity.Property(e => e.Email).HasMaxLength(150);
            entity.Property(e => e.Phone).HasMaxLength(50);
            entity.Property(e => e.CreditLimit).HasColumnType("decimal(18, 4)");
            entity.Property(e => e.DiscountPercentage).HasColumnType("decimal(5, 2)");
            entity.HasIndex(c => c.Email).IsUnique().HasFilter("[Email] IS NOT NULL");

            // Ignore navigation properties to avoid shadow properties
            entity.Ignore(e => e.Transactions);
        });
    }
}
