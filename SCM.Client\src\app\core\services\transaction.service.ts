import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import {
  TransactionHeader,
  CreateProductRequest,
  CreateProductOrder,
  CreateReceiving,
  TransactionDetail
} from '../models/transaction.model';

@Injectable({
  providedIn: 'root'
})
export class TransactionService {
  private readonly path = 'transactions';

  constructor(private apiService: ApiService) { }

  // General transaction methods
  getAll(): Observable<TransactionHeader[]> {
    return this.apiService.get<TransactionHeader[]>(this.path);
  }

  getById(id: number): Observable<TransactionHeader> {
    return this.apiService.get<TransactionHeader>(`${this.path}/${id}`);
  }

  getByProcessId(processId: number): Observable<TransactionHeader[]> {
    return this.apiService.get<TransactionHeader[]>(`${this.path}/process/${processId}`);
  }

  getByStageTypeId(stageTypeId: number): Observable<TransactionHeader[]> {
    return this.apiService.get<TransactionHeader[]>(`${this.path}/stage/${stageTypeId}`);
  }

  getReceivingTransactionsAvailableForCreditNote(): Observable<TransactionHeader[]> {
    return this.apiService.get<TransactionHeader[]>(`/api/receiving/available-for-credit-note`);
  }

  getByStatus(status: string): Observable<TransactionHeader[]> {
    return this.apiService.get<TransactionHeader[]>(`${this.path}/status/${status}`);
  }

  getByCostCenterId(costCenterId: number): Observable<TransactionHeader[]> {
    return this.apiService.get<TransactionHeader[]>(`${this.path}/costcenter/${costCenterId}`);
  }

  getBySupplierId(supplierId: number): Observable<TransactionHeader[]> {
    return this.apiService.get<TransactionHeader[]>(`${this.path}/supplier/${supplierId}`);
  }

  // Product Request methods (Stage 1)
  createProductRequest(request: CreateProductRequest): Observable<TransactionHeader> {
    return this.apiService.post<TransactionHeader, CreateProductRequest>(`${this.path}/product-request`, request);
  }

  updateProductRequest(id: number, request: any): Observable<void> {
    return this.apiService.put<void, any>(`${this.path}/product-request/${id}`, request);
  }

  submitProductRequest(id: number, notes?: string): Observable<void> {
    return this.apiService.put<void, any>(`${this.path}/product-request/${id}/submit`, { notes });
  }

  approveProductRequest(id: number, notes?: string): Observable<void> {
    return this.apiService.put<void, any>(`${this.path}/product-request/${id}/approve`, { notes });
  }

  rejectProductRequest(id: number, reason: string): Observable<void> {
    return this.apiService.put<void, any>(`${this.path}/product-request/${id}/reject`, { reason });
  }

  cancelProductRequest(id: number, reason?: string): Observable<void> {
    return this.apiService.put<void, any>(`${this.path}/product-request/${id}/cancel`, { reason });
  }

  // Product Order methods (Stage 2)
  createProductOrder(order: CreateProductOrder): Observable<TransactionHeader> {
    return this.apiService.post<TransactionHeader, CreateProductOrder>(
      `${this.path}/product-order`,
      order
    );
  }

  createProductOrderFromRequest(productRequestId: number, order: CreateProductOrder): Observable<TransactionHeader> {
    return this.apiService.post<TransactionHeader, CreateProductOrder>(
      `${this.path}/product-order/from-request/${productRequestId}`,
      order
    );
  }

  updateProductOrder(id: number, order: any): Observable<void> {
    return this.apiService.put<void, any>(`${this.path}/product-order/${id}`, order);
  }

  submitProductOrder(id: number, notes?: string): Observable<void> {
    return this.apiService.put<void, any>(`${this.path}/product-order/${id}/submit`, { notes });
  }

  approveProductOrder(id: number, notes?: string): Observable<void> {
    return this.apiService.put<void, any>(`${this.path}/product-order/${id}/approve`, { notes });
  }

  rejectProductOrder(id: number, reason: string): Observable<void> {
    return this.apiService.put<void, any>(`${this.path}/product-order/${id}/reject`, { reason });
  }

  cancelProductOrder(id: number, reason?: string): Observable<void> {
    return this.apiService.put<void, any>(`${this.path}/product-order/${id}/cancel`, { reason });
  }

  // Receiving methods (Stage 3)
  getAllReceivings(): Observable<TransactionHeader[]> {
    return this.apiService.get<TransactionHeader[]>('receiving');
  }

  getReceivingById(id: number): Observable<TransactionHeader> {
    return this.apiService.get<TransactionHeader>(`receiving/${id}`);
  }

  createReceiving(receiving: CreateReceiving): Observable<TransactionHeader> {
    return this.apiService.post<TransactionHeader, CreateReceiving>(
      'receiving',
      receiving
    );
  }

  createReceivingFromOrder(productOrderId: number, receiving: CreateReceiving): Observable<TransactionHeader> {
    return this.apiService.post<TransactionHeader, CreateReceiving>(
      `receiving/from-order/${productOrderId}`,
      receiving
    );
  }

  updateReceiving(id: number, receiving: any): Observable<void> {
    return this.apiService.put<void, any>(`receiving/${id}`, receiving);
  }

  submitReceiving(id: number, notes?: string): Observable<void> {
    return this.apiService.put<void, any>(`receiving/${id}/submit`, { notes });
  }

  approveReceiving(id: number, notes?: string): Observable<void> {
    return this.apiService.put<void, any>(`receiving/${id}/approve`, { notes });
  }

  rejectReceiving(id: number, reason: string): Observable<void> {
    return this.apiService.put<void, any>(`receiving/${id}/reject`, { reason });
  }

  completeReceiving(id: number, notes?: string): Observable<void> {
    return this.apiService.put<void, any>(`receiving/${id}/complete`, { notes });
  }

  cancelReceiving(id: number, reason?: string): Observable<void> {
    return this.apiService.put<void, any>(`receiving/${id}/cancel`, { reason });
  }

  // Transaction detail methods
  addTransactionDetail(transactionId: number, detail: any): Observable<TransactionDetail> {
    return this.apiService.post<TransactionDetail, any>(`${this.path}/${transactionId}/details`, detail);
  }

  updateTransactionDetail(id: number, detail: any): Observable<void> {
    return this.apiService.put<void, any>(`${this.path}/details/${id}`, detail);
  }

  deleteTransactionDetail(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/details/${id}`);
  }
}
