import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, FormArray, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDialogModule } from '@angular/material/dialog';
import { MatAutocompleteModule } from '@angular/material/autocomplete';

import { CreditNoteService } from '../../../../core/services/credit-note.service';
import { SupplierService, SupplierListItem } from '../../../../core/services/supplier.service';
import { CostCenterService } from '../../../../core/services/cost-center.service';
import { ProductService } from '../../../../core/services/product.service';
import { TransactionService } from '../../../../core/services/transaction.service';

import {
  CreditNote,
  CreateCreditNote,
  UpdateCreditNote,
  CreditNoteReason,
  CreditNoteStatus
} from '../../../../core/models/credit-note.model';
import { CostCenterList } from '../../../../core/models/cost-center.model';
import { Product } from '../../../../core/models/product.model';
import { TransactionHeader } from '../../../../core/models/transaction.model';

@Component({
  selector: 'app-credit-note-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatTableModule,
    MatChipsModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatDialogModule,
    MatAutocompleteModule
  ],
  templateUrl: './credit-note-detail.component.html',
  styleUrls: ['./credit-note-detail.component.scss']
})
export class CreditNoteDetailComponent implements OnInit {
  creditNoteForm!: FormGroup;
  creditNote: CreditNote | null = null;
  isEditMode = false;
  isViewMode = false;
  loading = false;
  saving = false;

  suppliers: SupplierListItem[] = [];
  costCenters: CostCenterList[] = [];
  products: Product[] = [];
  allReceivingTransactions: TransactionHeader[] = [];
  receivingTransactions: TransactionHeader[] = [];

  creditNoteReasons = Object.values(CreditNoteReason);
  creditNoteStatuses = CreditNoteStatus;

  displayedColumns: string[] = [
    'productCode',
    'productName',
    'quantity',
    'unitPrice',
    'lineTotal',
    'reason',
    'actions'
  ];

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private creditNoteService: CreditNoteService,
    private supplierService: SupplierService,
    private costCenterService: CostCenterService,
    private productService: ProductService,
    private transactionService: TransactionService,
    private snackBar: MatSnackBar
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.loadMasterData();
    this.checkRouteMode();
  }

  private initializeForm(): void {
    this.creditNoteForm = this.fb.group({
      sourceCostCenterId: ['', Validators.required],
      supplierId: [''],
      relatedTransactionId: [''],
      transactionDate: [new Date(), Validators.required],
      referenceNumber: [''],
      reason: ['', Validators.required],
      notes: [''],
      details: this.fb.array([])
    });
  }

  private checkRouteMode(): void {
    const id = this.route.snapshot.paramMap.get('id');
    const mode = this.route.snapshot.paramMap.get('mode');
    const fromReceiving = this.route.snapshot.queryParamMap.get('fromReceiving');
    const receivingNumber = this.route.snapshot.queryParamMap.get('receivingNumber');

    if (id && id !== 'new') {
      if (mode === 'edit') {
        this.isEditMode = true;
        this.loadCreditNote(+id);
      } else {
        this.isViewMode = true;
        this.loadCreditNote(+id);
        this.creditNoteForm.disable();
      }
    } else if (fromReceiving) {
      // Pre-populate form with receiving transaction data
      this.loadReceivingTransactionData(+fromReceiving);
    }
  }

  private loadMasterData(): void {
    // Load suppliers
    this.supplierService.getAll().subscribe({
      next: (suppliers) => this.suppliers = suppliers,
      error: (error) => console.error('Error loading suppliers:', error)
    });

    // Load cost centers
    this.costCenterService.getAll().subscribe({
      next: (costCenters) => this.costCenters = costCenters,
      error: (error) => console.error('Error loading cost centers:', error)
    });

    // Load products
    this.productService.getAll().subscribe({
      next: (products) => this.products = products,
      error: (error) => console.error('Error loading products:', error)
    });

    // Load receiving transactions for related transaction selection (only completed ones)
    this.transactionService.getByStageTypeId(3).subscribe({ // 3 = Receiving stage
      next: (transactions) => {
        // Filter only completed receiving transactions
        this.allReceivingTransactions = transactions.filter(t => t.status === 'Completed');
        this.filterReceivingTransactionsByCostCenter();
      },
      error: (error) => console.error('Error loading receiving transactions:', error)
    });
  }

  private loadCreditNote(id: number): void {
    this.loading = true;
    this.creditNoteService.getById(id).subscribe({
      next: (creditNote) => {
        this.creditNote = creditNote;
        this.populateForm(creditNote);
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading credit note:', error);
        this.snackBar.open('Error loading credit note', 'Close', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  private loadReceivingTransactionData(receivingId: number): void {
    console.log('Loading receiving transaction data for ID:', receivingId);
    this.loading = true;
    this.transactionService.getById(receivingId).subscribe({
      next: (receivingTransaction) => {
        console.log('Received transaction data:', receivingTransaction);
        // Pre-populate form with receiving transaction data
        this.creditNoteForm.patchValue({
          sourceCostCenterId: receivingTransaction.sourceCostCenterId,
          supplierId: receivingTransaction.supplierId,
          relatedTransactionId: receivingTransaction.id,
          transactionDate: new Date(),
          referenceNumber: `CN-${receivingTransaction.referenceNumber}`,
          reason: 'Damaged Goods', // Default reason
          notes: `Credit note for receiving transaction: ${receivingTransaction.transactionNumber}`
        });

        // Pre-populate details with receiving transaction details
        const detailsArray = this.creditNoteForm.get('details') as FormArray;
        detailsArray.clear();
        console.log('Transaction details:', receivingTransaction.details);
        receivingTransaction.details.forEach(detail => {
          console.log('Adding detail:', detail);
          detailsArray.push(this.createDetailFormGroup({
            productId: detail.productId,
            batchId: detail.batchId,
            unitId: detail.unitId,
            quantity: 0, // User needs to specify quantity to credit
            unitPrice: detail.unitPrice,
            reason: 'Damaged Goods',
            notes: detail.notes
          }));
        });
        console.log('Final details array length:', detailsArray.length);

        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading receiving transaction:', error);
        this.snackBar.open('Error loading receiving transaction data', 'Close', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  private populateForm(creditNote: CreditNote): void {
    this.creditNoteForm.patchValue({
      sourceCostCenterId: creditNote.sourceCostCenterId,
      supplierId: creditNote.supplierId,
      relatedTransactionId: creditNote.relatedTransactionId,
      transactionDate: new Date(creditNote.transactionDate),
      referenceNumber: creditNote.referenceNumber,
      reason: creditNote.reason,
      notes: creditNote.notes
    });

    // Filter receiving transactions based on the selected cost center
    this.filterReceivingTransactionsByCostCenter();

    // Populate details
    const detailsArray = this.creditNoteForm.get('details') as FormArray;
    detailsArray.clear();
    creditNote.details.forEach(detail => {
      detailsArray.push(this.createDetailFormGroup(detail));
    });
  }

  get detailsArray(): FormArray {
    return this.creditNoteForm.get('details') as FormArray;
  }

  private createDetailFormGroup(detail?: any): FormGroup {
    return this.fb.group({
      id: [detail?.id || 0],
      productId: [detail?.productId || '', Validators.required],
      batchId: [detail?.batchId || ''],
      unitId: [detail?.unitId || ''],
      quantity: [detail?.quantity || 0, [Validators.required, Validators.min(0.01)]],
      unitPrice: [detail?.unitPrice || 0, [Validators.required, Validators.min(0)]],
      reason: [detail?.reason || '', Validators.required],
      notes: [detail?.notes || '']
    });
  }

  addDetail(): void {
    console.log('Adding new detail item');
    const newDetail = this.createDetailFormGroup();
    this.detailsArray.push(newDetail);
    console.log('Details array length:', this.detailsArray.length);
  }

  removeDetail(index: number): void {
    this.detailsArray.removeAt(index);
  }

  onRelatedTransactionChange(transactionId: number): void {
    console.log('Related transaction changed:', transactionId);
    if (transactionId) {
      this.loadReceivingTransactionData(transactionId);
    } else {
      // Clear the details if no transaction is selected
      console.log('Clearing details array');
      this.detailsArray.clear();
    }
  }

  getProductName(productId: number): string {
    const product = this.products.find(p => p.id === productId);
    return product ? product.name : '';
  }

  getProductCode(productId: number): string {
    const product = this.products.find(p => p.id === productId);
    return product ? product.code : '';
  }

  calculateLineTotal(detail: any): number {
    return (detail.get('quantity')?.value || 0) * (detail.get('unitPrice')?.value || 0);
  }

  calculateTotalAmount(): number {
    return this.detailsArray.controls.reduce((total, detail) => {
      return total + this.calculateLineTotal(detail);
    }, 0);
  }

  onSubmit(): void {
    if (this.creditNoteForm.valid) {
      this.saving = true;
      const formValue = this.creditNoteForm.value;

      if (this.isEditMode && this.creditNote) {
        const updateData: UpdateCreditNote = {
          id: this.creditNote.id,
          sourceCostCenterId: formValue.sourceCostCenterId,
          supplierId: formValue.supplierId,
          transactionDate: formValue.transactionDate,
          referenceNumber: formValue.referenceNumber,
          reason: formValue.reason,
          notes: formValue.notes
        };

        this.creditNoteService.update(this.creditNote.id, updateData).subscribe({
          next: () => {
            this.snackBar.open('Credit note updated successfully', 'Close', { duration: 3000 });
            this.router.navigate(['/transactions/credit-notes']);
            this.saving = false;
          },
          error: (error) => {
            console.error('Error updating credit note:', error);
            this.snackBar.open('Error updating credit note', 'Close', { duration: 3000 });
            this.saving = false;
          }
        });
      } else {
        const createData: CreateCreditNote = {
          sourceCostCenterId: formValue.sourceCostCenterId,
          supplierId: formValue.supplierId,
          relatedTransactionId: formValue.relatedTransactionId,
          transactionDate: formValue.transactionDate,
          referenceNumber: formValue.referenceNumber,
          reason: formValue.reason,
          notes: formValue.notes,
          details: formValue.details.map((detail: any) => ({
            productId: detail.productId,
            batchId: detail.batchId,
            unitId: detail.unitId,
            quantity: detail.quantity,
            unitPrice: detail.unitPrice,
            reason: detail.reason,
            notes: detail.notes
          }))
        };

        // Check if we're creating from a receiving transaction
        const fromReceiving = this.route.snapshot.queryParamMap.get('fromReceiving');

        if (fromReceiving) {
          // Create credit note from receiving transaction
          this.creditNoteService.createFromReceiving(+fromReceiving, createData).subscribe({
            next: () => {
              this.snackBar.open('Credit note created successfully from receiving transaction', 'Close', { duration: 3000 });
              this.router.navigate(['/transactions/credit-notes']);
              this.saving = false;
            },
            error: (error) => {
              console.error('Error creating credit note from receiving:', error);
              this.snackBar.open('Error creating credit note from receiving', 'Close', { duration: 3000 });
              this.saving = false;
            }
          });
        } else {
          // Create regular credit note
          this.creditNoteService.create(createData).subscribe({
            next: () => {
              this.snackBar.open('Credit note created successfully', 'Close', { duration: 3000 });
              this.router.navigate(['/transactions/credit-notes']);
              this.saving = false;
            },
            error: (error) => {
              console.error('Error creating credit note:', error);
              this.snackBar.open('Error creating credit note', 'Close', { duration: 3000 });
              this.saving = false;
            }
          });
        }
      }
    }
  }

  onCancel(): void {
    this.router.navigate(['/transactions/credit-notes']);
  }

  onCostCenterChange(): void {
    // Clear related transaction when cost center changes
    this.creditNoteForm.patchValue({
      relatedTransactionId: null
    });

    // Filter receiving transactions by the selected cost center
    this.filterReceivingTransactionsByCostCenter();
  }

  private filterReceivingTransactionsByCostCenter(): void {
    const selectedCostCenterId = this.creditNoteForm.get('sourceCostCenterId')?.value;
    if (selectedCostCenterId) {
      this.receivingTransactions = this.allReceivingTransactions.filter(
        transaction => transaction.sourceCostCenterId === selectedCostCenterId
      );
    } else {
      this.receivingTransactions = [];
    }
  }
}
