using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class StockService : IStockService
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;

    public StockService(
        ApplicationDbContext dbContext,
        IMapper mapper)
    {
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<IEnumerable<StockOnHandDto>> GetStockOnHandAsync()
    {
        var stockOnHand = await _dbContext.StockOnHand
            .Include(s => s.Product)
            .Include(s => s.CostCenter)
            .Include(s => s.Unit)
            .ToListAsync();

        return _mapper.Map<IEnumerable<StockOnHandDto>>(stockOnHand);
    }

    public async Task<IEnumerable<StockOnHandDto>> GetStockOnHandByProductIdAsync(int productId)
    {
        try
        {
            // First, try to get stock without includes to check if the table exists and has data
            var stockExists = await _dbContext.StockOnHand
                .Where(s => s.ProductId == productId)
                .AnyAsync();

            if (!stockExists)
            {
                // Return empty list if no stock found
                return new List<StockOnHandDto>();
            }

            // Try to get stock with minimal includes first
            var stockOnHand = await _dbContext.StockOnHand
                .Where(s => s.ProductId == productId)
                .ToListAsync();

            // Manually load related entities to avoid schema issues
            var result = new List<StockOnHandDto>();
            foreach (var stock in stockOnHand)
            {
                var dto = new StockOnHandDto
                {
                    ProductId = stock.ProductId,
                    CostCenterId = stock.CostCenterId,
                    UnitId = stock.UnitId,
                    Quantity = stock.Quantity,
                    CostPrice = stock.CostPrice,
                    LastUpdated = stock.LastUpdated
                };

                // Try to load product name
                try
                {
                    var product = await _dbContext.Products.FindAsync(stock.ProductId);
                    if (product != null)
                    {
                        dto.ProductName = product.Name;
                        dto.ProductCode = product.Code ?? "";
                    }
                }
                catch
                {
                    dto.ProductName = $"Product {stock.ProductId}";
                    dto.ProductCode = "";
                }

                // Try to load cost center name
                try
                {
                    var costCenter = await _dbContext.CostCenters.FindAsync(stock.CostCenterId);
                    if (costCenter != null)
                    {
                        dto.CostCenterName = costCenter.Name;
                    }
                }
                catch
                {
                    dto.CostCenterName = $"Cost Center {stock.CostCenterId}";
                }

                // Batch information not available in StockOnHand table
                dto.BatchNumber = "N/A";

                // Try to load unit name
                if (stock.UnitId.HasValue)
                {
                    try
                    {
                        var unit = await _dbContext.Units.FindAsync(stock.UnitId.Value);
                        if (unit != null)
                        {
                            dto.UnitName = unit.Name;
                        }
                    }
                    catch
                    {
                        dto.UnitName = $"Unit {stock.UnitId}";
                    }
                }

                result.Add(dto);
            }

            return result;
        }
        catch (Exception)
        {
            // If there's any database schema issue, return empty list
            return new List<StockOnHandDto>();
        }
    }

    public async Task<IEnumerable<StockOnHandDto>> GetStockOnHandByCostCenterIdAsync(int costCenterId)
    {
        var stockOnHand = await _dbContext.StockOnHand
            .Include(s => s.Product)
            .Include(s => s.CostCenter)
            .Include(s => s.Unit)
            .Where(s => s.CostCenterId == costCenterId)
            .ToListAsync();

        return _mapper.Map<IEnumerable<StockOnHandDto>>(stockOnHand);
    }

    public async Task<StockOnHandDto?> GetStockOnHandAsync(int productId, int costCenterId)
    {
        var stockOnHand = await _dbContext.StockOnHand
            .Include(s => s.Product)
            .Include(s => s.CostCenter)
            .Include(s => s.Unit)
            .FirstOrDefaultAsync(s =>
                s.ProductId == productId &&
                s.CostCenterId == costCenterId);

        return stockOnHand != null ? _mapper.Map<StockOnHandDto>(stockOnHand) : null;
    }

    public async Task<IEnumerable<StockOnHandSummaryDto>> GetStockSummaryByCostCenterIdAsync(int costCenterId)
    {
        var stockSummary = await _dbContext.StockOnHand
            .Where(s => s.CostCenterId == costCenterId)
            .GroupBy(s => new { s.ProductId, s.CostCenterId })
            .Select(g => new
            {
                ProductId = g.Key.ProductId,
                CostCenterId = g.Key.CostCenterId,
                TotalQuantity = g.Sum(s => s.Quantity),
                AverageCostPrice = g.Average(s => s.CostPrice)
            })
            .ToListAsync();

        var result = new List<StockOnHandSummaryDto>();

        foreach (var item in stockSummary)
        {
            var product = await _dbContext.Products
                .Include(p => p.Unit)
                .FirstOrDefaultAsync(p => p.Id == item.ProductId);

            var costCenter = await _dbContext.CostCenters
                .FirstOrDefaultAsync(c => c.Id == item.CostCenterId);

            var productCostCenterLink = await _dbContext.ProductCostCenterLinks
                .FirstOrDefaultAsync(pcl =>
                    pcl.ProductId == item.ProductId &&
                    pcl.CostCenterId == item.CostCenterId);

            var minStock = productCostCenterLink?.MinStock ?? product?.MinStock;
            var maxStock = productCostCenterLink?.MaxStock ?? product?.MaxStock;
            var reorderPoint = productCostCenterLink?.ReorderPoint ?? product?.ReorderPoint;

            var summary = new StockOnHandSummaryDto
            {
                ProductId = item.ProductId,
                ProductName = product?.Name ?? string.Empty,
                ProductCode = product?.Code ?? string.Empty,
                CostCenterId = item.CostCenterId,
                CostCenterName = costCenter?.Name ?? string.Empty,
                TotalQuantity = item.TotalQuantity,
                AverageCostPrice = item.AverageCostPrice,
                UnitName = product?.Unit?.Name,
                MinStock = minStock,
                MaxStock = maxStock,
                ReorderPoint = reorderPoint,
                IsLowStock = minStock.HasValue && item.TotalQuantity < minStock.Value,
                IsOverStock = maxStock.HasValue && item.TotalQuantity > maxStock.Value,
                NeedsReorder = reorderPoint.HasValue && item.TotalQuantity <= reorderPoint.Value
            };

            result.Add(summary);
        }

        return result;
    }

    public async Task<IEnumerable<StockOnHandSummaryDto>> GetLowStockItemsAsync(int? costCenterId = null)
    {
        var allStockSummaries = new List<StockOnHandSummaryDto>();

        if (costCenterId.HasValue)
        {
            allStockSummaries.AddRange(await GetStockSummaryByCostCenterIdAsync(costCenterId.Value));
        }
        else
        {
            var costCenters = await _dbContext.CostCenters.ToListAsync();
            foreach (var cc in costCenters)
            {
                allStockSummaries.AddRange(await GetStockSummaryByCostCenterIdAsync(cc.Id));
            }
        }

        return allStockSummaries.Where(s => s.IsLowStock).ToList();
    }

    public async Task<IEnumerable<StockOnHandSummaryDto>> GetOverStockItemsAsync(int? costCenterId = null)
    {
        var allStockSummaries = new List<StockOnHandSummaryDto>();

        if (costCenterId.HasValue)
        {
            allStockSummaries.AddRange(await GetStockSummaryByCostCenterIdAsync(costCenterId.Value));
        }
        else
        {
            var costCenters = await _dbContext.CostCenters.ToListAsync();
            foreach (var cc in costCenters)
            {
                allStockSummaries.AddRange(await GetStockSummaryByCostCenterIdAsync(cc.Id));
            }
        }

        return allStockSummaries.Where(s => s.IsOverStock).ToList();
    }

    public async Task<IEnumerable<StockOnHandSummaryDto>> GetReorderItemsAsync(int? costCenterId = null)
    {
        var allStockSummaries = new List<StockOnHandSummaryDto>();

        if (costCenterId.HasValue)
        {
            allStockSummaries.AddRange(await GetStockSummaryByCostCenterIdAsync(costCenterId.Value));
        }
        else
        {
            var costCenters = await _dbContext.CostCenters.ToListAsync();
            foreach (var cc in costCenters)
            {
                allStockSummaries.AddRange(await GetStockSummaryByCostCenterIdAsync(cc.Id));
            }
        }

        return allStockSummaries.Where(s => s.NeedsReorder).ToList();
    }

    public async Task<IEnumerable<StockOnHandDto>> GetExpiringStockAsync(int daysToExpiry, int? costCenterId = null)
    {
        var expiryDate = DateTime.UtcNow.AddDays(daysToExpiry);

        var query = _dbContext.StockOnHand
            .Include(s => s.Product)
            .Include(s => s.CostCenter)
            .Include(s => s.Unit)
            .Where(s => s.Quantity > 0);

        if (costCenterId.HasValue)
        {
            query = query.Where(s => s.CostCenterId == costCenterId.Value);
        }

        var expiringStock = await query.ToListAsync();
        return _mapper.Map<IEnumerable<StockOnHandDto>>(expiringStock);
    }

    public async Task AdjustStockAsync(StockAdjustmentDto stockAdjustmentDto)
    {
        var stockOnHand = await _dbContext.StockOnHand
            .FirstOrDefaultAsync(s =>
                s.ProductId == stockAdjustmentDto.ProductId &&
                s.CostCenterId == stockAdjustmentDto.CostCenterId);

        if (stockOnHand == null)
        {
            // Create new stock record if it doesn't exist
            stockOnHand = new StockOnHand
            {
                ProductId = stockAdjustmentDto.ProductId,
                CostCenterId = stockAdjustmentDto.CostCenterId,
                UnitId = stockAdjustmentDto.UnitId,
                Quantity = stockAdjustmentDto.Quantity,
                CostPrice = stockAdjustmentDto.CostPrice,
                LastUpdated = DateTime.UtcNow
            };

            _dbContext.StockOnHand.Add(stockOnHand);
        }
        else
        {
            // Update existing stock record
            stockOnHand.Quantity = stockAdjustmentDto.Quantity;
            if (stockAdjustmentDto.CostPrice.HasValue)
            {
                stockOnHand.CostPrice = stockAdjustmentDto.CostPrice;
            }
            stockOnHand.LastUpdated = DateTime.UtcNow;

            _dbContext.StockOnHand.Update(stockOnHand);
        }

        // Update product average cost if needed
        if (stockAdjustmentDto.CostPrice.HasValue)
        {
            await UpdateProductAverageCostAsync(stockAdjustmentDto.ProductId);
        }

        await _dbContext.SaveChangesAsync();

        // TODO: Create stock adjustment transaction record
    }

    public async Task AddStockAsync(StockAddDto stockAddDto)
    {
        Console.WriteLine($"DEBUG StockService: AddStockAsync called - ProductId: {stockAddDto.ProductId}, CostCenterId: {stockAddDto.CostCenterId}, Quantity: {stockAddDto.Quantity}, CostPrice: {stockAddDto.CostPrice}");

        var stockOnHand = await _dbContext.StockOnHand
            .FirstOrDefaultAsync(s =>
                s.ProductId == stockAddDto.ProductId &&
                s.CostCenterId == stockAddDto.CostCenterId);

        Console.WriteLine($"DEBUG StockService: Existing stock found: {stockOnHand != null}, Current quantity: {stockOnHand?.Quantity ?? 0}");

        if (stockOnHand == null)
        {
            Console.WriteLine($"DEBUG StockService: Creating new stock record");
            // Create new stock record if it doesn't exist
            stockOnHand = new StockOnHand
            {
                ProductId = stockAddDto.ProductId,
                CostCenterId = stockAddDto.CostCenterId,
                UnitId = stockAddDto.UnitId,
                Quantity = stockAddDto.Quantity,
                CostPrice = stockAddDto.CostPrice,
                LastUpdated = DateTime.UtcNow
            };

            _dbContext.StockOnHand.Add(stockOnHand);
            Console.WriteLine($"DEBUG StockService: New stock record added - Quantity: {stockOnHand.Quantity}, CostPrice: {stockOnHand.CostPrice}");
        }
        else
        {
            Console.WriteLine($"DEBUG StockService: Updating existing stock record - Old Quantity: {stockOnHand.Quantity}, Adding: {stockAddDto.Quantity}");
            // Update existing stock record
            var oldQuantity = stockOnHand.Quantity;
            stockOnHand.Quantity += stockAddDto.Quantity;
            if (stockAddDto.CostPrice.HasValue)
            {
                // Calculate weighted average cost
                var totalValue = oldQuantity * stockOnHand.CostPrice.GetValueOrDefault() +
                                stockAddDto.Quantity * stockAddDto.CostPrice.Value;
                stockOnHand.CostPrice = totalValue / stockOnHand.Quantity;
                Console.WriteLine($"DEBUG StockService: Calculated weighted average cost: {stockOnHand.CostPrice}");
            }
            stockOnHand.LastUpdated = DateTime.UtcNow;

            _dbContext.StockOnHand.Update(stockOnHand);
            Console.WriteLine($"DEBUG StockService: Updated stock record - New Quantity: {stockOnHand.Quantity}, New CostPrice: {stockOnHand.CostPrice}");
        }

        // Update product average cost if needed
        if (stockAddDto.CostPrice.HasValue)
        {
            Console.WriteLine($"DEBUG StockService: Updating product average cost for ProductId: {stockAddDto.ProductId}");
            await UpdateProductAverageCostAsync(stockAddDto.ProductId);
        }

        Console.WriteLine($"DEBUG StockService: Saving changes to database");
        await _dbContext.SaveChangesAsync();
        Console.WriteLine($"DEBUG StockService: Changes saved successfully");

        // TODO: Create stock transaction record
    }

    private async Task UpdateProductAverageCostAsync(int productId)
    {
        var product = await _dbContext.Products.FindAsync(productId);
        if (product == null)
            return;

        var stockItems = await _dbContext.StockOnHand
            .Where(s => s.ProductId == productId && s.Quantity > 0 && s.CostPrice.HasValue)
            .ToListAsync();

        if (stockItems.Any())
        {
            var totalValue = stockItems.Sum(s => s.Quantity * s.CostPrice.GetValueOrDefault());
            var totalQuantity = stockItems.Sum(s => s.Quantity);

            if (totalQuantity > 0)
            {
                // Update average cost (weighted average of all stock)
                product.AverageCost = totalValue / totalQuantity;

                // Update cost price to the most recent received price (last cost price from stock)
                var latestStockItem = stockItems.OrderByDescending(s => s.LastUpdated).FirstOrDefault();
                if (latestStockItem?.CostPrice.HasValue == true)
                {
                    product.CostPrice = latestStockItem.CostPrice;
                }

                _dbContext.Products.Update(product);
                Console.WriteLine($"DEBUG StockService: Updated Product {productId} - CostPrice: {product.CostPrice}, AverageCost: {product.AverageCost}");
            }
        }
    }

    public async Task AdjustStockQuantityAsync(
        int productId,
        int costCenterId,
        decimal quantity,
        int unitId,
        string source,
        int? transactionId = null)
    {
        // Find existing stock record
        var stockOnHand = await _dbContext.StockOnHand
            .FirstOrDefaultAsync(s =>
                s.ProductId == productId &&
                s.CostCenterId == costCenterId);

        if (stockOnHand == null)
        {
            // Create new stock record if it doesn't exist
            stockOnHand = new StockOnHand
            {
                ProductId = productId,
                CostCenterId = costCenterId,
                UnitId = unitId,
                Quantity = quantity,
                LastUpdated = DateTime.UtcNow
            };

            // Get product cost price if available
            var product = await _dbContext.Products.FindAsync(productId);
            if (product != null && product.AverageCost.HasValue)
            {
                stockOnHand.CostPrice = product.AverageCost.Value;
            }

            _dbContext.StockOnHand.Add(stockOnHand);
        }
        else
        {
            // Update existing stock record
            stockOnHand.Quantity += quantity;
            stockOnHand.LastUpdated = DateTime.UtcNow;

            _dbContext.StockOnHand.Update(stockOnHand);
        }

        // Create stock transaction record (using default batch ID since StockOnHand doesn't track batches)
        var stockTransaction = new StockTransaction
        {
            ProductId = productId,
            CostCenterId = costCenterId,
            BatchId = 1, // Default batch ID since StockOnHand doesn't track batches
            UnitId = unitId,
            Quantity = quantity,
            TransactionDate = DateTime.UtcNow,
            Source = source,
            TransactionId = transactionId,
            CreatedAt = DateTime.UtcNow,
            IsActive = true
        };

        _dbContext.StockTransactions.Add(stockTransaction);

        await _dbContext.SaveChangesAsync();
    }
}
