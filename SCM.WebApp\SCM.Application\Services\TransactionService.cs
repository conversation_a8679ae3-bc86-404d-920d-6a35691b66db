using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class TransactionService : ITransactionService
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly IStockService _stockService;
    private readonly IUserPermissionService _userPermissionService;

    public TransactionService(ApplicationDbContext dbContext, IMapper mapper, IStockService stockService, IUserPermissionService userPermissionService)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _stockService = stockService;
        _userPermissionService = userPermissionService;
    }

    #region Common Methods

    public async Task<IEnumerable<TransactionHeaderDto>> GetAllTransactionsAsync()
    {
        var transactions = await _dbContext.TransactionHeaders
            .Include(t => t.Details)
            .OrderByDescending(t => t.TransactionDate)
            .ToListAsync();

        // Load related entities for each transaction
        foreach (var transaction in transactions)
        {
            // Load related entities for each detail
            foreach (var detail in transaction.Details)
            {
                // Load Product data
                var product = await _dbContext.Products.FindAsync(detail.ProductId);
                if (product != null)
                {
                    detail.Product = product;
                }

                // Load Unit data
                var unit = await _dbContext.Units.FindAsync(detail.UnitId);
                if (unit != null)
                {
                    detail.Unit = unit;
                }

                // Load Tax data if available
                if (detail.TaxId.HasValue)
                {
                    var tax = await _dbContext.Taxes.FindAsync(detail.TaxId.Value);
                    if (tax != null)
                    {
                        detail.Tax = tax;
                    }
                }
            }

            // Load related entities for the transaction header
            if (transaction.StageTypeId.HasValue)
            {
                transaction.StageType = await _dbContext.TransactionStageTypes.FindAsync(transaction.StageTypeId.Value);
            }

            if (transaction.SourceCostCenterId.HasValue)
            {
                transaction.SourceCostCenter = await _dbContext.CostCenters.FindAsync(transaction.SourceCostCenterId.Value);
            }

            if (transaction.DestinationCostCenterId.HasValue)
            {
                transaction.DestinationCostCenter = await _dbContext.CostCenters.FindAsync(transaction.DestinationCostCenterId.Value);
            }

            if (transaction.SupplierId.HasValue)
            {
                transaction.Supplier = await _dbContext.Suppliers.FindAsync(transaction.SupplierId.Value);
            }
        }

        var dtos = _mapper.Map<IEnumerable<TransactionHeaderDto>>(transactions);

        // Manually set the navigation property names in the DTOs
        foreach (var dto in dtos)
        {
            var transaction = transactions.FirstOrDefault(t => t.Id == dto.Id);
            if (transaction != null)
            {
                SetNavigationPropertyNames(dto, transaction);
            }
        }

        return dtos;
    }

    public async Task<TransactionHeaderDto?> GetTransactionByIdAsync(int id)
    {
        var transaction = await _dbContext.TransactionHeaders
            .Include(t => t.Details)
            .FirstOrDefaultAsync(t => t.Id == id);

        if (transaction != null)
        {
            // Manually load related entities for each detail
            foreach (var detail in transaction.Details)
            {
                // Load Product data
                var product = await _dbContext.Products.FindAsync(detail.ProductId);
                if (product != null)
                {
                    detail.Product = product;
                }

                // Load Unit data
                var unit = await _dbContext.Units.FindAsync(detail.UnitId);
                if (unit != null)
                {
                    detail.Unit = unit;
                }

                // Load Tax data if available
                if (detail.TaxId.HasValue)
                {
                    var tax = await _dbContext.Taxes.FindAsync(detail.TaxId.Value);
                    if (tax != null)
                    {
                        detail.Tax = tax;
                    }
                }
            }

            // Load related entities for the transaction header
            if (transaction.StageTypeId.HasValue)
            {
                transaction.StageType = await _dbContext.TransactionStageTypes.FindAsync(transaction.StageTypeId.Value);
            }

            if (transaction.SourceCostCenterId.HasValue)
            {
                transaction.SourceCostCenter = await _dbContext.CostCenters.FindAsync(transaction.SourceCostCenterId.Value);
            }

            if (transaction.DestinationCostCenterId.HasValue)
            {
                transaction.DestinationCostCenter = await _dbContext.CostCenters.FindAsync(transaction.DestinationCostCenterId.Value);
            }

            if (transaction.SupplierId.HasValue)
            {
                transaction.Supplier = await _dbContext.Suppliers.FindAsync(transaction.SupplierId.Value);
            }
        }

        if (transaction == null) return null;

        var dto = _mapper.Map<TransactionHeaderDto>(transaction);
        SetNavigationPropertyNames(dto, transaction);

        return dto;
    }

    // Removed GetTransactionsByProcessIdAsync method as we no longer use ProcessId

    public async Task<IEnumerable<TransactionHeaderDto>> GetTransactionsByStageTypeIdAsync(int stageTypeId)
    {
        var transactions = await _dbContext.TransactionHeaders
            .Include(t => t.Details)
            .Where(t => t.StageTypeId == stageTypeId)
            .OrderByDescending(t => t.TransactionDate)
            .ToListAsync();

        // Load related entities for each transaction
        foreach (var transaction in transactions)
        {
            // Load related entities for each detail
            foreach (var detail in transaction.Details)
            {
                // Load Product data
                var product = await _dbContext.Products.FindAsync(detail.ProductId);
                if (product != null)
                {
                    detail.Product = product;
                }

                // Load Unit data
                var unit = await _dbContext.Units.FindAsync(detail.UnitId);
                if (unit != null)
                {
                    detail.Unit = unit;
                }

                // Load Tax data if available
                if (detail.TaxId.HasValue)
                {
                    var tax = await _dbContext.Taxes.FindAsync(detail.TaxId.Value);
                    if (tax != null)
                    {
                        detail.Tax = tax;
                    }
                }
            }

            // Load related entities for the transaction header
            if (transaction.StageTypeId.HasValue)
            {
                transaction.StageType = await _dbContext.TransactionStageTypes.FindAsync(transaction.StageTypeId.Value);
            }

            if (transaction.SourceCostCenterId.HasValue)
            {
                transaction.SourceCostCenter = await _dbContext.CostCenters.FindAsync(transaction.SourceCostCenterId.Value);
            }

            if (transaction.DestinationCostCenterId.HasValue)
            {
                transaction.DestinationCostCenter = await _dbContext.CostCenters.FindAsync(transaction.DestinationCostCenterId.Value);
            }

            if (transaction.SupplierId.HasValue)
            {
                transaction.Supplier = await _dbContext.Suppliers.FindAsync(transaction.SupplierId.Value);
            }
        }

        var dtos = _mapper.Map<IEnumerable<TransactionHeaderDto>>(transactions);

        // Manually set the navigation property names in the DTOs
        foreach (var dto in dtos)
        {
            var transaction = transactions.FirstOrDefault(t => t.Id == dto.Id);
            if (transaction != null)
            {
                SetNavigationPropertyNames(dto, transaction);
            }
        }

        return dtos;
    }

    public async Task<IEnumerable<TransactionHeaderDto>> GetTransactionsByStatusAsync(string status)
    {
        var transactions = await _dbContext.TransactionHeaders
            .Include(t => t.Details)
            .Where(t => t.Status == status)
            .OrderByDescending(t => t.TransactionDate)
            .ToListAsync();

        // Load related entities for each transaction
        foreach (var transaction in transactions)
        {
            // Load related entities for each detail
            foreach (var detail in transaction.Details)
            {
                // Load Product data
                var product = await _dbContext.Products.FindAsync(detail.ProductId);
                if (product != null)
                {
                    detail.Product = product;
                }

                // Load Unit data
                var unit = await _dbContext.Units.FindAsync(detail.UnitId);
                if (unit != null)
                {
                    detail.Unit = unit;
                }

                // Load Tax data if available
                if (detail.TaxId.HasValue)
                {
                    var tax = await _dbContext.Taxes.FindAsync(detail.TaxId.Value);
                    if (tax != null)
                    {
                        detail.Tax = tax;
                    }
                }
            }

            // Load related entities for the transaction header
            if (transaction.StageTypeId.HasValue)
            {
                transaction.StageType = await _dbContext.TransactionStageTypes.FindAsync(transaction.StageTypeId.Value);
            }

            if (transaction.SourceCostCenterId.HasValue)
            {
                transaction.SourceCostCenter = await _dbContext.CostCenters.FindAsync(transaction.SourceCostCenterId.Value);
            }

            if (transaction.DestinationCostCenterId.HasValue)
            {
                transaction.DestinationCostCenter = await _dbContext.CostCenters.FindAsync(transaction.DestinationCostCenterId.Value);
            }

            if (transaction.SupplierId.HasValue)
            {
                transaction.Supplier = await _dbContext.Suppliers.FindAsync(transaction.SupplierId.Value);
            }
        }

        var dtos = _mapper.Map<IEnumerable<TransactionHeaderDto>>(transactions);

        // Manually set the navigation property names in the DTOs
        foreach (var dto in dtos)
        {
            var transaction = transactions.FirstOrDefault(t => t.Id == dto.Id);
            if (transaction != null)
            {
                SetNavigationPropertyNames(dto, transaction);
            }
        }

        return dtos;
    }

    public async Task<IEnumerable<TransactionHeaderDto>> GetTransactionsByCostCenterAsync(int costCenterId)
    {
        var transactions = await _dbContext.TransactionHeaders
            .Include(t => t.Details)
            .Where(t => t.SourceCostCenterId == costCenterId || t.DestinationCostCenterId == costCenterId)
            .OrderByDescending(t => t.TransactionDate)
            .ToListAsync();

        // Load related entities for each transaction
        foreach (var transaction in transactions)
        {
            // Load related entities for each detail
            foreach (var detail in transaction.Details)
            {
                // Load Product data
                var product = await _dbContext.Products.FindAsync(detail.ProductId);
                if (product != null)
                {
                    detail.Product = product;
                }

                // Load Unit data
                var unit = await _dbContext.Units.FindAsync(detail.UnitId);
                if (unit != null)
                {
                    detail.Unit = unit;
                }

                // Load Tax data if available
                if (detail.TaxId.HasValue)
                {
                    var tax = await _dbContext.Taxes.FindAsync(detail.TaxId.Value);
                    if (tax != null)
                    {
                        detail.Tax = tax;
                    }
                }
            }

            // Load related entities for the transaction header
            if (transaction.StageTypeId.HasValue)
            {
                transaction.StageType = await _dbContext.TransactionStageTypes.FindAsync(transaction.StageTypeId.Value);
            }

            if (transaction.SourceCostCenterId.HasValue)
            {
                transaction.SourceCostCenter = await _dbContext.CostCenters.FindAsync(transaction.SourceCostCenterId.Value);
            }

            if (transaction.DestinationCostCenterId.HasValue)
            {
                transaction.DestinationCostCenter = await _dbContext.CostCenters.FindAsync(transaction.DestinationCostCenterId.Value);
            }

            if (transaction.SupplierId.HasValue)
            {
                transaction.Supplier = await _dbContext.Suppliers.FindAsync(transaction.SupplierId.Value);
            }
        }

        var dtos = _mapper.Map<IEnumerable<TransactionHeaderDto>>(transactions);

        // Manually set the navigation property names in the DTOs
        foreach (var dto in dtos)
        {
            var transaction = transactions.FirstOrDefault(t => t.Id == dto.Id);
            if (transaction != null)
            {
                SetNavigationPropertyNames(dto, transaction);
            }
        }

        return dtos;
    }

    public async Task<IEnumerable<TransactionHeaderDto>> GetTransactionsBySupplierAsync(int supplierId)
    {
        var transactions = await _dbContext.TransactionHeaders
            .Include(t => t.Details)
            .Where(t => t.SupplierId == supplierId)
            .OrderByDescending(t => t.TransactionDate)
            .ToListAsync();

        // Load related entities for each transaction
        foreach (var transaction in transactions)
        {
            // Load related entities for each detail
            foreach (var detail in transaction.Details)
            {
                // Load Product data
                var product = await _dbContext.Products.FindAsync(detail.ProductId);
                if (product != null)
                {
                    detail.Product = product;
                }

                // Load Unit data
                var unit = await _dbContext.Units.FindAsync(detail.UnitId);
                if (unit != null)
                {
                    detail.Unit = unit;
                }

                // Load Tax data if available
                if (detail.TaxId.HasValue)
                {
                    var tax = await _dbContext.Taxes.FindAsync(detail.TaxId.Value);
                    if (tax != null)
                    {
                        detail.Tax = tax;
                    }
                }
            }

            // Load related entities for the transaction header
            if (transaction.StageTypeId.HasValue)
            {
                transaction.StageType = await _dbContext.TransactionStageTypes.FindAsync(transaction.StageTypeId.Value);
            }

            if (transaction.SourceCostCenterId.HasValue)
            {
                transaction.SourceCostCenter = await _dbContext.CostCenters.FindAsync(transaction.SourceCostCenterId.Value);
            }

            if (transaction.DestinationCostCenterId.HasValue)
            {
                transaction.DestinationCostCenter = await _dbContext.CostCenters.FindAsync(transaction.DestinationCostCenterId.Value);
            }

            if (transaction.SupplierId.HasValue)
            {
                transaction.Supplier = await _dbContext.Suppliers.FindAsync(transaction.SupplierId.Value);
            }
        }

        var dtos = _mapper.Map<IEnumerable<TransactionHeaderDto>>(transactions);

        // Manually set the navigation property names in the DTOs
        foreach (var dto in dtos)
        {
            var transaction = transactions.FirstOrDefault(t => t.Id == dto.Id);
            if (transaction != null)
            {
                SetNavigationPropertyNames(dto, transaction);
            }
        }

        return dtos;
    }

    public async Task<IEnumerable<TransactionHeaderDto>> GetTransactionsByCustomerAsync(int customerId)
    {
        // This method is no longer functional as CustomerId property has been removed
        // Return an empty list for now
        return new List<TransactionHeaderDto>();
    }

    public async Task<IEnumerable<TransactionHeaderDto>> GetTransactionsByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        var transactions = await _dbContext.TransactionHeaders
            .Include(t => t.Details)
            .Where(t => t.TransactionDate >= startDate && t.TransactionDate <= endDate)
            .OrderByDescending(t => t.TransactionDate)
            .ToListAsync();

        // Load related entities for each transaction
        foreach (var transaction in transactions)
        {
            // Load related entities for each detail
            foreach (var detail in transaction.Details)
            {
                // Load Product data
                var product = await _dbContext.Products.FindAsync(detail.ProductId);
                if (product != null)
                {
                    detail.Product = product;
                }

                // Load Unit data
                var unit = await _dbContext.Units.FindAsync(detail.UnitId);
                if (unit != null)
                {
                    detail.Unit = unit;
                }

                // Load Tax data if available
                if (detail.TaxId.HasValue)
                {
                    var tax = await _dbContext.Taxes.FindAsync(detail.TaxId.Value);
                    if (tax != null)
                    {
                        detail.Tax = tax;
                    }
                }
            }

            // Load related entities for the transaction header
            if (transaction.StageTypeId.HasValue)
            {
                transaction.StageType = await _dbContext.TransactionStageTypes.FindAsync(transaction.StageTypeId.Value);
            }

            if (transaction.SourceCostCenterId.HasValue)
            {
                transaction.SourceCostCenter = await _dbContext.CostCenters.FindAsync(transaction.SourceCostCenterId.Value);
            }

            if (transaction.DestinationCostCenterId.HasValue)
            {
                transaction.DestinationCostCenter = await _dbContext.CostCenters.FindAsync(transaction.DestinationCostCenterId.Value);
            }

            if (transaction.SupplierId.HasValue)
            {
                transaction.Supplier = await _dbContext.Suppliers.FindAsync(transaction.SupplierId.Value);
            }
        }

        var dtos = _mapper.Map<IEnumerable<TransactionHeaderDto>>(transactions);

        // Manually set the navigation property names in the DTOs
        foreach (var dto in dtos)
        {
            var transaction = transactions.FirstOrDefault(t => t.Id == dto.Id);
            if (transaction != null)
            {
                SetNavigationPropertyNames(dto, transaction);
            }
        }

        return dtos;
    }



    public async Task<IEnumerable<TransactionStageTypeDto>> GetAllTransactionStageTypesAsync()
    {
        var stageTypes = await _dbContext.TransactionStageTypes
            .OrderBy(st => st.Name)
            .ToListAsync();

        return _mapper.Map<IEnumerable<TransactionStageTypeDto>>(stageTypes);
    }

    // Removed GetAllTransactionProcessesAsync method as we no longer use TransactionProcess

    private async Task<string> GenerateReferenceNumberAsync(string prefix)
    {
        // Get the current date in yyyyMMdd format
        string dateString = DateTime.Now.ToString("yyyyMMdd");

        // Get the count of transactions with the same prefix for today
        int count = await _dbContext.TransactionHeaders
            .CountAsync(t => t.TransactionNumber.StartsWith($"{prefix}-{dateString}"));

        // Generate the reference number
        return $"{prefix}-{dateString}-{(count + 1).ToString("D4")}";
    }

    private async Task UpdateTransactionTotalAsync(int transactionId)
    {
        var transaction = await _dbContext.TransactionHeaders
            .Include(t => t.Details)
            .FirstOrDefaultAsync(t => t.Id == transactionId);

        if (transaction == null)
            return;

        // Ensure all line totals are calculated correctly
        foreach (var detail in transaction.Details)
        {
            // Recalculate line total based on quantity and unit price
            decimal lineTotal = detail.Quantity * detail.UnitPrice;

            // Apply discount if any
            if ((detail.DiscountPercentage ?? 0m) > 0)
            {
                decimal detailDiscountAmount = lineTotal * ((detail.DiscountPercentage ?? 0m) / 100m);
                detail.DiscountAmount = detailDiscountAmount;
                lineTotal -= detailDiscountAmount;
            }

            // Apply tax if any
            if (detail.TaxId.HasValue && (detail.TaxRate ?? 0m) > 0)
            {
                decimal detailTaxAmount = lineTotal * ((detail.TaxRate ?? 0m) / 100m);
                detail.TaxAmount = detailTaxAmount;
                lineTotal += detailTaxAmount;
            }

            detail.LineTotal = lineTotal;
        }

        // Calculate subtotal (sum of line totals before tax)
        decimal subTotal = transaction.Details.Sum(d => d.LineTotal - (d.TaxAmount ?? 0));

        // Calculate tax amount
        decimal totalTaxAmount = transaction.Details.Sum(d => d.TaxAmount ?? 0);

        // Calculate discount amount
        decimal totalDiscountAmount = transaction.Details.Sum(d => d.DiscountAmount ?? 0);

        // Calculate total amount
        decimal totalAmount = transaction.Details.Sum(d => d.LineTotal);

        // Update the transaction
        transaction.SubTotal = subTotal;
        transaction.TaxAmount = totalTaxAmount;
        transaction.DiscountAmount = totalDiscountAmount;
        transaction.TotalAmount = totalAmount;

        await _dbContext.SaveChangesAsync();
    }

    private Task<bool> UserHasFullReceivePermissionAsync(int userId)
    {
        return _userPermissionService.UserHasFullReceivePermissionAsync(userId);
    }

    #endregion

    #region Product Request Methods

    public async Task<TransactionHeaderDto> CreateProductRequestAsync(CreateProductRequestDto createProductRequestDto, int userId)
    {
        // Removed dependency on TransactionProcess

        // Get the Request stage type
        var requestStage = await _dbContext.TransactionStageTypes
            .FirstOrDefaultAsync(tst => tst.Name == "Request");
        if (requestStage == null)
            throw new InvalidOperationException("Request stage type not found.");

        // Create the transaction header
        var transaction = new TransactionHeader
        {
            TransactionNumber = await GenerateReferenceNumberAsync("PR"),
            ReferenceNumber = createProductRequestDto.ReferenceNumber,
            StageTypeId = requestStage.Id,
            TransactionTypeId = 1, // Default to Purchase transaction type
            SourceCostCenterId = createProductRequestDto.SourceCostCenterId,
            TransactionDate = createProductRequestDto.TransactionDate,
            // Remove RequiredDate as it's causing shadow property issues
            Status = "Draft",
            Notes = createProductRequestDto.Notes,
            CreatedById = userId,
            CreatedAt = DateTime.UtcNow,
            IsActive = true,
            SubTotal = 0m,
            TaxAmount = 0m,
            TotalAmount = 0m,
            DiscountAmount = 0m,
            DiscountPercentage = 0m
        };

        _dbContext.TransactionHeaders.Add(transaction);
        await _dbContext.SaveChangesAsync();

        // Add details if provided
        if (createProductRequestDto.Details != null && createProductRequestDto.Details.Any())
        {
            foreach (var detailDto in createProductRequestDto.Details)
            {
                var detail = new TransactionDetail
                {
                    TransactionId = transaction.Id,
                    ProductId = detailDto.ProductId,
                    BatchId = detailDto.BatchId,
                    UnitId = detailDto.UnitId ?? 1, // Default to first unit if not specified
                    Quantity = detailDto.Quantity,
                    UnitPrice = detailDto.UnitPrice ?? 0, // Default to 0 if not specified
                    TaxId = detailDto.TaxId,
                    DiscountPercentage = detailDto.DiscountPercentage,
                    Notes = detailDto.Notes,
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true
                };

                // Calculate tax amount and total amount
                if (detailDto.UnitPrice.HasValue)
                {
                    decimal lineTotal = detailDto.Quantity * detailDto.UnitPrice.Value;

                    // Apply discount if any
                    if (detailDto.DiscountPercentage.HasValue && detailDto.DiscountPercentage.Value > 0)
                    {
                        decimal detailDiscountAmount = lineTotal * (detailDto.DiscountPercentage.Value / 100);
                        detail.DiscountAmount = detailDiscountAmount;
                        lineTotal -= detailDiscountAmount;
                    }

                    // Apply tax if any
                    if (detailDto.TaxId.HasValue)
                    {
                        var tax = await _dbContext.Taxes.FindAsync(detailDto.TaxId.Value);
                        if (tax != null)
                        {
                            detail.TaxRate = tax.Rate;
                            decimal detailTaxAmount = lineTotal * (tax.Rate / 100);
                            detail.TaxAmount = detailTaxAmount;
                            lineTotal += detailTaxAmount;
                        }
                    }

                    detail.LineTotal = lineTotal;
                }
                else
                {
                    detail.LineTotal = 0;
                }

                _dbContext.TransactionDetails.Add(detail);
            }

            await _dbContext.SaveChangesAsync();
            await UpdateTransactionTotalAsync(transaction.Id);
        }

        // Reload the transaction with all details
        return await GetTransactionByIdAsync(transaction.Id);
    }

    public async Task UpdateProductRequestAsync(int id, UpdateProductRequestDto updateProductRequestDto)
    {
        var transaction = await _dbContext.TransactionHeaders
            .FirstOrDefaultAsync(t => t.Id == id);

        if (transaction == null)
            throw new InvalidOperationException("Product request not found.");

        if (transaction.Status != "Draft")
            throw new InvalidOperationException("Product request can only be updated when in Draft status.");

        transaction.SourceCostCenterId = updateProductRequestDto.SourceCostCenterId;
        transaction.TransactionDate = updateProductRequestDto.TransactionDate;
        // Remove RequiredDate as it's causing shadow property issues
        transaction.Notes = updateProductRequestDto.Notes;
        transaction.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();
    }

    public async Task SubmitProductRequestAsync(int id, string? notes = null)
    {
        var transaction = await _dbContext.TransactionHeaders
            .FirstOrDefaultAsync(t => t.Id == id);

        if (transaction == null)
            throw new InvalidOperationException("Product request not found.");

        if (transaction.Status != "Draft")
            throw new InvalidOperationException("Product request can only be submitted when in Draft status.");

        transaction.Status = "Submitted";
        transaction.Notes = string.IsNullOrEmpty(notes)
            ? transaction.Notes
            : $"{transaction.Notes}\nSubmission Notes: {notes}";
        transaction.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();
    }

    public async Task ApproveProductRequestAsync(int id, int userId, string? notes = null)
    {
        var transaction = await _dbContext.TransactionHeaders
            .FirstOrDefaultAsync(t => t.Id == id);

        if (transaction == null)
            throw new InvalidOperationException("Product request not found.");

        if (transaction.Status != "Submitted")
            throw new InvalidOperationException("Product request can only be approved when in Submitted status.");

        transaction.Status = "Approved";
        transaction.Notes = string.IsNullOrEmpty(notes)
            ? transaction.Notes
            : $"{transaction.Notes}\nApproval Notes: {notes}";
        transaction.ApprovedById = userId;
        transaction.ApprovedDate = DateTime.UtcNow;
        transaction.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();
    }

    public async Task RejectProductRequestAsync(int id, string reason)
    {
        var transaction = await _dbContext.TransactionHeaders
            .FirstOrDefaultAsync(t => t.Id == id);

        if (transaction == null)
            throw new InvalidOperationException("Product request not found.");

        if (transaction.Status != "Submitted")
            throw new InvalidOperationException("Product request can only be rejected when in Submitted status.");

        transaction.Status = "Rejected";
        transaction.Notes = $"{transaction.Notes}\nRejection Reason: {reason}";
        transaction.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();
    }

    public async Task CancelProductRequestAsync(int id, string? reason = null)
    {
        var transaction = await _dbContext.TransactionHeaders
            .FirstOrDefaultAsync(t => t.Id == id);

        if (transaction == null)
            throw new InvalidOperationException("Product request not found.");

        if (transaction.Status == "Approved" || transaction.Status == "Completed")
            throw new InvalidOperationException("Product request cannot be cancelled after it has been approved or completed.");

        transaction.Status = "Cancelled";
        transaction.Notes = string.IsNullOrEmpty(reason)
            ? transaction.Notes
            : $"{transaction.Notes}\nCancellation Reason: {reason}";
        transaction.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();
    }

    #endregion

    #region Product Order Methods

    public async Task<TransactionHeaderDto> CreateProductOrderFromRequestAsync(int productRequestId, CreateProductOrderDto createProductOrderDto, int userId)
    {
        // Get the product request
        var productRequest = await _dbContext.TransactionHeaders
            .Include(t => t.Details)
            .FirstOrDefaultAsync(t => t.Id == productRequestId);

        if (productRequest == null)
            throw new InvalidOperationException("Product request not found.");

        if (productRequest.Status != "Approved")
            throw new InvalidOperationException("Product order can only be created from an approved product request.");

        // Get the Order stage type
        var orderStage = await _dbContext.TransactionStageTypes
            .FirstOrDefaultAsync(tst => tst.Name == "Order");
        if (orderStage == null)
            throw new InvalidOperationException("Order stage type not found.");

        // Create the transaction header
        var transaction = new TransactionHeader
        {
            TransactionNumber = await GenerateReferenceNumberAsync("PO"),
            ReferenceNumber = createProductOrderDto.ReferenceNumber,
            StageTypeId = orderStage.Id,
            TransactionTypeId = 1, // Purchase transaction type
            SourceCostCenterId = productRequest.SourceCostCenterId,
            SupplierId = createProductOrderDto.SupplierId,
            TransactionDate = createProductOrderDto.TransactionDate,
            RequiredDate = createProductOrderDto.RequiredDate,
            Status = "Draft",
            Notes = createProductOrderDto.Notes,
            CreatedById = userId,
            CreatedAt = DateTime.UtcNow,
            IsActive = true,
            SubTotal = 0m,
            TaxAmount = 0m,
            TotalAmount = 0m,
            DiscountAmount = 0m,
            DiscountPercentage = 0m
        };

        _dbContext.TransactionHeaders.Add(transaction);
        await _dbContext.SaveChangesAsync();

        // Copy details from the product request
        foreach (var requestDetail in productRequest.Details)
        {
            var detail = new TransactionDetail
            {
                TransactionId = transaction.Id,
                ProductId = requestDetail.ProductId,
                BatchId = requestDetail.BatchId,
                UnitId = requestDetail.UnitId,
                Quantity = requestDetail.Quantity,
                UnitPrice = createProductOrderDto.UpdatePrices ? await GetLatestProductPriceAsync(requestDetail.ProductId) : requestDetail.UnitPrice,
                TaxId = requestDetail.TaxId,
                TaxRate = requestDetail.TaxRate,
                DiscountPercentage = requestDetail.DiscountPercentage,
                Notes = requestDetail.Notes,
                CreatedAt = DateTime.UtcNow,
                IsActive = true
            };

            // Recalculate line total based on quantity and unit price
            decimal lineTotal = detail.Quantity * detail.UnitPrice;

            // Apply discount if any
            if ((detail.DiscountPercentage ?? 0m) > 0)
            {
                decimal detailDiscountAmount = lineTotal * ((detail.DiscountPercentage ?? 0m) / 100m);
                detail.DiscountAmount = detailDiscountAmount;
                lineTotal -= detailDiscountAmount;
            }

            // Apply tax if any
            if (detail.TaxId.HasValue && (detail.TaxRate ?? 0m) > 0)
            {
                decimal detailTaxAmount = lineTotal * ((detail.TaxRate ?? 0m) / 100m);
                detail.TaxAmount = detailTaxAmount;
                lineTotal += detailTaxAmount;
            }

            detail.LineTotal = lineTotal;

            _dbContext.TransactionDetails.Add(detail);
        }

        await _dbContext.SaveChangesAsync();
        await UpdateTransactionTotalAsync(transaction.Id);

        // Reload the transaction with all details
        return await GetTransactionByIdAsync(transaction.Id);
    }

    private async Task<decimal> GetLatestProductPriceAsync(int productId)
    {
        // Get the latest price from the most recent purchase order
        // Join with TransactionHeader to avoid using navigation properties
        var latestPrice = await (from td in _dbContext.TransactionDetails
                                join th in _dbContext.TransactionHeaders on td.TransactionId equals th.Id
                                where td.ProductId == productId &&
                                      th.StageTypeId == 2 && // Order stage
                                      td.UnitPrice > 0
                                orderby th.TransactionDate descending
                                select td.UnitPrice)
                                .FirstOrDefaultAsync();

        if (latestPrice > 0)
            return latestPrice;

        // If no purchase order price found, get the price from the product
        var product = await _dbContext.Products.FindAsync(productId);
        return product?.CostPrice ?? 0;
    }

    public async Task<TransactionHeaderDto> CreateProductOrderAsync(CreateProductOrderDto createProductOrderDto, int userId)
    {
        // Removed dependency on TransactionType and TransactionProcess

        // Get the Order stage type
        var orderStage = await _dbContext.TransactionStageTypes
            .FirstOrDefaultAsync(tst => tst.Name == "Order");
        if (orderStage == null)
            throw new InvalidOperationException("Order stage type not found.");

        // Create the transaction header
        var transaction = new TransactionHeader
        {
            TransactionNumber = await GenerateReferenceNumberAsync("PO"),
            ReferenceNumber = createProductOrderDto.ReferenceNumber,
            StageTypeId = orderStage.Id,
            TransactionTypeId = 1, // Purchase transaction type
            SourceCostCenterId = createProductOrderDto.SourceCostCenterId,
            SupplierId = createProductOrderDto.SupplierId,
            TransactionDate = createProductOrderDto.TransactionDate,
            RequiredDate = createProductOrderDto.RequiredDate,
            Status = "Draft",
            Notes = createProductOrderDto.Notes,
            CreatedById = userId,
            CreatedAt = DateTime.UtcNow,
            IsActive = true,
            SubTotal = 0m,
            TaxAmount = 0m,
            TotalAmount = 0m,
            DiscountAmount = 0m,
            DiscountPercentage = 0m
        };

        _dbContext.TransactionHeaders.Add(transaction);
        await _dbContext.SaveChangesAsync();

        // Add details if provided
        if (createProductOrderDto.Details != null && createProductOrderDto.Details.Any())
        {
            foreach (var detailDto in createProductOrderDto.Details)
            {
                var detail = new TransactionDetail
                {
                    TransactionId = transaction.Id,
                    ProductId = detailDto.ProductId,
                    BatchId = detailDto.BatchId,
                    UnitId = detailDto.UnitId ?? 1, // Default to first unit if not specified
                    Quantity = detailDto.Quantity,
                    UnitPrice = detailDto.UnitPrice ?? 0, // Default to 0 if not specified
                    TaxId = detailDto.TaxId,
                    DiscountPercentage = detailDto.DiscountPercentage,
                    Notes = detailDto.Notes,
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true
                };

                // Calculate tax amount and total amount
                decimal lineTotal = detailDto.Quantity * (detailDto.UnitPrice ?? 0);

                // Apply discount if any
                if (detailDto.DiscountPercentage.HasValue && detailDto.DiscountPercentage.Value > 0)
                {
                    decimal detailDiscountAmount = lineTotal * (detailDto.DiscountPercentage.Value / 100);
                    detail.DiscountAmount = detailDiscountAmount;
                    lineTotal -= detailDiscountAmount;
                }

                // Apply tax if any
                if (detailDto.TaxId.HasValue)
                {
                    var tax = await _dbContext.Taxes.FindAsync(detailDto.TaxId.Value);
                    if (tax != null)
                    {
                        detail.TaxRate = tax.Rate;
                        decimal detailTaxAmount = lineTotal * (tax.Rate / 100);
                        detail.TaxAmount = detailTaxAmount;
                        lineTotal += detailTaxAmount;
                    }
                }

                detail.LineTotal = lineTotal;

                _dbContext.TransactionDetails.Add(detail);
            }

            await _dbContext.SaveChangesAsync();
            await UpdateTransactionTotalAsync(transaction.Id);
        }

        // Reload the transaction with all details
        return await GetTransactionByIdAsync(transaction.Id);
    }

    public async Task UpdateProductOrderAsync(int id, UpdateProductOrderDto updateProductOrderDto)
    {
        var transaction = await _dbContext.TransactionHeaders
            .FirstOrDefaultAsync(t => t.Id == id);

        if (transaction == null)
            throw new InvalidOperationException("Product order not found.");

        if (transaction.Status != "Draft")
            throw new InvalidOperationException("Product order can only be updated when in Draft status.");

        transaction.SourceCostCenterId = updateProductOrderDto.SourceCostCenterId;
        transaction.SupplierId = updateProductOrderDto.SupplierId;
        transaction.TransactionDate = updateProductOrderDto.TransactionDate;
        transaction.RequiredDate = updateProductOrderDto.RequiredDate;
        transaction.Notes = updateProductOrderDto.Notes;
        transaction.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();
    }

    public async Task SubmitProductOrderAsync(int id, string? notes = null)
    {
        var transaction = await _dbContext.TransactionHeaders
            .FirstOrDefaultAsync(t => t.Id == id);

        if (transaction == null)
            throw new InvalidOperationException("Product order not found.");

        if (transaction.Status != "Draft")
            throw new InvalidOperationException("Product order can only be submitted when in Draft status.");

        transaction.Status = "Submitted";
        transaction.Notes = string.IsNullOrEmpty(notes)
            ? transaction.Notes
            : $"{transaction.Notes}\nSubmission Notes: {notes}";
        transaction.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();
    }

    public async Task ApproveProductOrderAsync(int id, int userId, string? notes = null)
    {
        var transaction = await _dbContext.TransactionHeaders
            .FirstOrDefaultAsync(t => t.Id == id);

        if (transaction == null)
            throw new InvalidOperationException("Product order not found.");

        if (transaction.Status != "Submitted")
            throw new InvalidOperationException("Product order can only be approved when in Submitted status.");

        transaction.Status = "Approved";
        transaction.Notes = string.IsNullOrEmpty(notes)
            ? transaction.Notes
            : $"{transaction.Notes}\nApproval Notes: {notes}";
        transaction.ApprovedById = userId;
        transaction.ApprovedDate = DateTime.UtcNow;
        transaction.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();
    }

    public async Task RejectProductOrderAsync(int id, string reason)
    {
        var transaction = await _dbContext.TransactionHeaders
            .FirstOrDefaultAsync(t => t.Id == id);

        if (transaction == null)
            throw new InvalidOperationException("Product order not found.");

        if (transaction.Status != "Submitted")
            throw new InvalidOperationException("Product order can only be rejected when in Submitted status.");

        transaction.Status = "Rejected";
        transaction.Notes = $"{transaction.Notes}\nRejection Reason: {reason}";
        transaction.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();
    }

    public async Task CancelProductOrderAsync(int id, string? reason = null)
    {
        var transaction = await _dbContext.TransactionHeaders
            .FirstOrDefaultAsync(t => t.Id == id);

        if (transaction == null)
            throw new InvalidOperationException("Product order not found.");

        if (transaction.Status == "Approved" || transaction.Status == "Completed")
            throw new InvalidOperationException("Product order cannot be cancelled after it has been approved or completed.");

        transaction.Status = "Cancelled";
        transaction.Notes = string.IsNullOrEmpty(reason)
            ? transaction.Notes
            : $"{transaction.Notes}\nCancellation Reason: {reason}";
        transaction.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();
    }

    #endregion

    #region Receiving Methods

    public async Task<TransactionHeaderDto> CreateReceivingFromOrderAsync(int productOrderId, CreateReceivingDto createReceivingDto, int userId)
    {
        // Get the product order
        var productOrder = await _dbContext.TransactionHeaders
            .Include(t => t.Details)
            .FirstOrDefaultAsync(t => t.Id == productOrderId);

        if (productOrder == null)
            throw new InvalidOperationException("Product order not found.");

        if (productOrder.Status != "Approved")
            throw new InvalidOperationException("Receiving can only be created from an approved product order.");



        // Get the Receiving stage type
        var receivingStage = await _dbContext.TransactionStageTypes
            .FirstOrDefaultAsync(tst => tst.Name == "Receiving");
        if (receivingStage == null)
            throw new InvalidOperationException("Receiving stage type not found.");

        // Generate the transaction number
        var transactionNumber = await GenerateReferenceNumberAsync("GR");

        // Create the transaction header
        var transaction = new TransactionHeader
        {
            TransactionNumber = transactionNumber,
            ReferenceNumber = string.IsNullOrEmpty(createReceivingDto.ReferenceNumber) ? transactionNumber : createReceivingDto.ReferenceNumber,

            // Removed ProcessId
            StageTypeId = receivingStage.Id,
            TransactionTypeId = 1, // Purchase transaction type
            SourceCostCenterId = productOrder.SourceCostCenterId,
            SupplierId = productOrder.SupplierId,
            TransactionDate = createReceivingDto.TransactionDate,
            Status = "Draft",
            Notes = createReceivingDto.Notes,
            // Removed ExternalReference property
            RelatedTransactionId = productOrder.Id,
            RelatedTransactionNumber = productOrder.ReferenceNumber,
            CreatedById = userId,
            CreatedAt = DateTime.UtcNow,
            IsActive = true
        };



        _dbContext.TransactionHeaders.Add(transaction);
        await _dbContext.SaveChangesAsync();

        // Copy details from the product order
        foreach (var orderDetail in productOrder.Details)
        {
            var detail = new TransactionDetail
            {
                TransactionId = transaction.Id,
                ProductId = orderDetail.ProductId,
                BatchId = orderDetail.BatchId,
                UnitId = orderDetail.UnitId,
                Quantity = orderDetail.Quantity, // Default to ordered quantity
                UnitPrice = orderDetail.UnitPrice,
                TaxId = orderDetail.TaxId,
                TaxRate = orderDetail.TaxRate,
                TaxAmount = orderDetail.TaxAmount,
                DiscountPercentage = orderDetail.DiscountPercentage,
                DiscountAmount = orderDetail.DiscountAmount,
                LineTotal = orderDetail.LineTotal,
                Notes = orderDetail.Notes,
                CreatedAt = DateTime.UtcNow,
                IsActive = true
            };

            _dbContext.TransactionDetails.Add(detail);
        }

        await _dbContext.SaveChangesAsync();
        await UpdateTransactionTotalAsync(transaction.Id);

        // Reload the transaction with all details
        return await GetTransactionByIdAsync(transaction.Id);
    }

    public async Task<TransactionHeaderDto> CreateReceivingAsync(CreateReceivingDto createReceivingDto, int userId)
    {


        // Removed dependency on TransactionProcess

        // Get the Receiving stage type
        var receivingStage = await _dbContext.TransactionStageTypes
            .FirstOrDefaultAsync(tst => tst.Name == "Receiving");
        if (receivingStage == null)
            throw new InvalidOperationException("Receiving stage type not found.");

        // Check if user has permission to create receiving without order
        bool hasPermission = await UserHasFullReceivePermissionAsync(userId);
        if (!hasPermission)
            throw new UnauthorizedAccessException("You do not have permission to create receiving without a product order.");

        // Generate the transaction number
        var transactionNumber = await GenerateReferenceNumberAsync("GR");

        // Create the transaction header
        var transaction = new TransactionHeader
        {
            TransactionNumber = transactionNumber,
            ReferenceNumber = string.IsNullOrEmpty(createReceivingDto.ReferenceNumber) ? transactionNumber : createReceivingDto.ReferenceNumber,

            // Removed ProcessId
            StageTypeId = receivingStage.Id,
            TransactionTypeId = 1, // Purchase transaction type
            SourceCostCenterId = createReceivingDto.SourceCostCenterId,
            SupplierId = createReceivingDto.SupplierId,
            TransactionDate = createReceivingDto.TransactionDate,
            Status = "Draft",
            Notes = createReceivingDto.Notes,
            // Removed ExternalReference property
            CreatedById = userId,
            CreatedAt = DateTime.UtcNow,
            IsSkippedStep = true,
            IsActive = true
        };

        _dbContext.TransactionHeaders.Add(transaction);
        await _dbContext.SaveChangesAsync();

        // Add details if provided
        if (createReceivingDto.Details != null && createReceivingDto.Details.Any())
        {
            foreach (var detailDto in createReceivingDto.Details)
            {
                var detail = new TransactionDetail
                {
                    TransactionId = transaction.Id,
                    ProductId = detailDto.ProductId,
                    BatchId = detailDto.BatchId,
                    UnitId = detailDto.UnitId ?? 1, // Default to first unit if not specified
                    Quantity = detailDto.Quantity,
                    UnitPrice = detailDto.UnitPrice ?? 0, // Default to 0 if not specified
                    TaxId = detailDto.TaxId,
                    DiscountPercentage = detailDto.DiscountPercentage,
                    Notes = detailDto.Notes,
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true
                };

                // Calculate tax amount and total amount
                if (detailDto.UnitPrice.HasValue)
                {
                    decimal lineTotal = detailDto.Quantity * detailDto.UnitPrice.Value;

                    // Apply discount if any
                    if (detailDto.DiscountPercentage.HasValue && detailDto.DiscountPercentage.Value > 0)
                    {
                        decimal discountAmount = lineTotal * (detailDto.DiscountPercentage.Value / 100);
                        detail.DiscountAmount = discountAmount;
                        lineTotal -= discountAmount;
                    }

                    // Apply tax if any
                    if (detailDto.TaxId.HasValue)
                    {
                        var tax = await _dbContext.Taxes.FindAsync(detailDto.TaxId.Value);
                        if (tax != null)
                        {
                            detail.TaxRate = tax.Rate;
                            decimal taxAmount = lineTotal * (tax.Rate / 100);
                            detail.TaxAmount = taxAmount;
                            lineTotal += taxAmount;
                        }
                    }

                    detail.LineTotal = lineTotal;
                }
                else
                {
                    detail.LineTotal = 0;
                }

                _dbContext.TransactionDetails.Add(detail);
            }

            await _dbContext.SaveChangesAsync();
            await UpdateTransactionTotalAsync(transaction.Id);
        }

        // Reload the transaction with all details
        return await GetTransactionByIdAsync(transaction.Id);
    }

    public async Task UpdateReceivingAsync(int id, UpdateReceivingDto updateReceivingDto)
    {
        var transaction = await _dbContext.TransactionHeaders
            .FirstOrDefaultAsync(t => t.Id == id);

        if (transaction == null)
            throw new InvalidOperationException("Receiving not found.");

        if (transaction.Status != "Draft")
            throw new InvalidOperationException("Receiving can only be updated when in Draft status.");

        transaction.SourceCostCenterId = updateReceivingDto.SourceCostCenterId;
        transaction.SupplierId = updateReceivingDto.SupplierId;
        transaction.TransactionDate = updateReceivingDto.TransactionDate;
        transaction.Notes = updateReceivingDto.Notes;
        // Removed ExternalReference property
        transaction.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();
    }

    public async Task UpdateReceivingDetailAsync(int id, int detailId, UpdateReceivingDetailDto updateDetailDto)
    {
        var transaction = await _dbContext.TransactionHeaders
            .FirstOrDefaultAsync(t => t.Id == id);

        if (transaction == null)
            throw new InvalidOperationException("Receiving not found.");

        if (transaction.Status != "Draft")
            throw new InvalidOperationException("Receiving can only be updated when in Draft status.");

        var detail = await _dbContext.TransactionDetails
            .FirstOrDefaultAsync(d => d.Id == detailId && d.TransactionId == id);

        if (detail == null)
            throw new InvalidOperationException("Receiving detail not found.");

        detail.Quantity = updateDetailDto.Quantity;
        detail.BatchId = updateDetailDto.BatchId ?? detail.BatchId;
        detail.UnitPrice = updateDetailDto.UnitPrice ?? detail.UnitPrice;
        detail.Notes = updateDetailDto.Notes ?? detail.Notes;
        detail.UpdatedAt = DateTime.UtcNow;

        // Recalculate line total
        decimal lineTotal = detail.Quantity * detail.UnitPrice;

        // Apply discount if any
        if ((detail.DiscountPercentage ?? 0m) > 0)
        {
            decimal discountAmount = lineTotal * ((detail.DiscountPercentage ?? 0m) / 100m);
            detail.DiscountAmount = discountAmount;
            lineTotal -= discountAmount;
        }

        // Apply tax if any
        if (detail.TaxId.HasValue && (detail.TaxRate ?? 0m) > 0)
        {
            decimal taxAmount = lineTotal * ((detail.TaxRate ?? 0m) / 100m);
            detail.TaxAmount = taxAmount;
            lineTotal += taxAmount;
        }

        detail.LineTotal = lineTotal;

        await _dbContext.SaveChangesAsync();
        await UpdateTransactionTotalAsync(id);
    }

    public async Task CompleteReceivingAsync(int id, int userId)
    {
        Console.WriteLine($"DEBUG: CompleteReceivingAsync called for transaction {id}");

        var transaction = await _dbContext.TransactionHeaders
            .Include(t => t.Details)
            .FirstOrDefaultAsync(t => t.Id == id);

        if (transaction == null)
        {
            Console.WriteLine($"DEBUG: Transaction {id} not found");
            throw new InvalidOperationException("Receiving not found.");
        }

        Console.WriteLine($"DEBUG: Transaction {id} found with status '{transaction.Status}' and {transaction.Details?.Count ?? 0} details");

        if (transaction.Status != "Draft" && transaction.Status != "Approved" && transaction.Status != "Submitted")
            throw new InvalidOperationException($"Receiving can only be completed when in Draft, Submitted, or Approved status. Current status: {transaction.Status}");

        if (!transaction.SourceCostCenterId.HasValue)
            throw new InvalidOperationException("Receiving must have a source cost center to complete.");

        if (!transaction.Details.Any())
            throw new InvalidOperationException("Receiving must have at least one detail item to complete.");

        // Update stock when completing (only if not already completed)
        if (transaction.Status != "Completed")
        {
            Console.WriteLine($"DEBUG: Completing receiving {transaction.Id} from {transaction.Status} status. Details count: {transaction.Details?.Count ?? 0}");

            foreach (var detail in transaction.Details)
            {
                Console.WriteLine($"DEBUG: Processing detail - ProductId: {detail.ProductId}, Quantity: {detail.Quantity}, UnitPrice: {detail.UnitPrice}");

                if (detail.Quantity <= 0)
                {
                    Console.WriteLine($"DEBUG: Skipping detail with zero/negative quantity: {detail.Quantity}");
                    continue; // Skip items with zero or negative quantity
                }

                try
                {
                    Console.WriteLine($"DEBUG: Adding stock for ProductId {detail.ProductId}, Quantity {detail.Quantity}, UnitPrice {detail.UnitPrice}, CostCenter {transaction.SourceCostCenterId.Value}, BatchId {detail.BatchId}, UnitId {detail.UnitId}");

                    var stockAddDto = new StockAddDto
                    {
                        ProductId = detail.ProductId,
                        CostCenterId = transaction.SourceCostCenterId.Value,
                        BatchId = detail.BatchId ?? 1, // Default to batch 1 if null
                        UnitId = detail.UnitId,
                        Quantity = detail.Quantity,
                        CostPrice = detail.UnitPrice,
                        TransactionDate = transaction.TransactionDate,
                        Source = "Receiving",
                        TransactionId = transaction.Id,
                        ReferenceNumber = transaction.ReferenceNumber ?? transaction.TransactionNumber,
                        ReferenceType = "Receiving"
                    };

                    Console.WriteLine($"DEBUG: StockAddDto created - ProductId: {stockAddDto.ProductId}, CostCenterId: {stockAddDto.CostCenterId}, BatchId: {stockAddDto.BatchId}, UnitId: {stockAddDto.UnitId}, Quantity: {stockAddDto.Quantity}, CostPrice: {stockAddDto.CostPrice}");

                    await _stockService.AddStockAsync(stockAddDto);

                    Console.WriteLine($"DEBUG: Successfully added stock for ProductId {detail.ProductId}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"DEBUG: Error adding stock for product {detail.ProductId}: {ex.Message}");
                    throw new InvalidOperationException($"Error adding stock for product {detail.ProductId}: {ex.Message}", ex);
                }
            }
        }
        else
        {
            Console.WriteLine($"DEBUG: Skipping stock update because transaction status is '{transaction.Status}', not 'Draft'");
        }
        // Note: If transaction was already approved, stock was updated during approval

        // Update transaction status
        transaction.Status = "Completed";
        transaction.UpdatedAt = DateTime.UtcNow;

        // Update related purchase order status if this receiving is linked to a purchase order
        if (transaction.RelatedTransactionId.HasValue)
        {
            var relatedOrder = await _dbContext.TransactionHeaders
                .Include(t => t.Details)
                .FirstOrDefaultAsync(t => t.Id == transaction.RelatedTransactionId.Value);

            if (relatedOrder != null)
            {
                // Check if all items have been fully received
                bool allItemsReceived = await CheckAllOrderItemsReceivedAsync(relatedOrder.Id);

                // Update the purchase order status
                relatedOrder.Status = allItemsReceived ? "Completed" : "Partially Received";
                relatedOrder.UpdatedAt = DateTime.UtcNow;

                _dbContext.TransactionHeaders.Update(relatedOrder);
            }
        }

        await _dbContext.SaveChangesAsync();
    }

    // Helper method to check if all items in a purchase order have been fully received
    private async Task<bool> CheckAllOrderItemsReceivedAsync(int orderId)
    {
        // Get the purchase order details
        var orderDetails = await _dbContext.TransactionDetails
            .Where(td => td.TransactionId == orderId)
            .ToListAsync();

        // Get all receiving transactions related to this purchase order
        var receivingTransactions = await _dbContext.TransactionHeaders
            .Include(t => t.Details)
            .Where(t => t.RelatedTransactionId == orderId && t.Status == "Completed")
            .ToListAsync();

        // Check each order detail
        foreach (var orderDetail in orderDetails)
        {
            decimal totalReceivedQuantity = 0;

            // Sum up the received quantities for this product
            foreach (var receiving in receivingTransactions)
            {
                var receivingDetail = receiving.Details
                    .FirstOrDefault(d => d.ProductId == orderDetail.ProductId);

                if (receivingDetail != null)
                {
                    totalReceivedQuantity += receivingDetail.Quantity;
                }
            }

            // If any product hasn't been fully received, return false
            if (totalReceivedQuantity < orderDetail.Quantity)
            {
                return false;
            }
        }

        // All items have been fully received
        return true;
    }

    public async Task SubmitReceivingAsync(int id, string? notes = null)
    {
        var transaction = await _dbContext.TransactionHeaders
            .FirstOrDefaultAsync(t => t.Id == id);

        if (transaction == null)
            throw new InvalidOperationException("Receiving not found.");

        if (transaction.Status != "Draft")
            throw new InvalidOperationException("Receiving can only be submitted when in Draft status.");

        transaction.Status = "Submitted";
        transaction.Notes = string.IsNullOrEmpty(notes)
            ? transaction.Notes
            : $"{transaction.Notes}\nSubmission Notes: {notes}";
        transaction.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();
    }

    public async Task ApproveReceivingAsync(int id, int userId, string? notes = null)
    {
        var transaction = await _dbContext.TransactionHeaders
            .Include(t => t.Details)
            .FirstOrDefaultAsync(t => t.Id == id);

        if (transaction == null)
            throw new InvalidOperationException("Receiving not found.");

        if (transaction.Status != "Submitted")
            throw new InvalidOperationException("Receiving can only be approved when in Submitted status.");

        if (!transaction.SourceCostCenterId.HasValue)
            throw new InvalidOperationException("Receiving must have a source cost center to approve.");

        if (!transaction.Details.Any())
            throw new InvalidOperationException("Receiving must have at least one detail item to approve.");

        // Note: Stock is updated during completion, not approval

        transaction.Status = "Approved";
        transaction.Notes = string.IsNullOrEmpty(notes)
            ? transaction.Notes
            : $"{transaction.Notes}\nApproval Notes: {notes}";
        transaction.ApprovedById = userId;
        transaction.ApprovedDate = DateTime.UtcNow;
        transaction.UpdatedAt = DateTime.UtcNow;

        // Update related purchase order status if this receiving is linked to a purchase order
        if (transaction.RelatedTransactionId.HasValue)
        {
            var relatedOrder = await _dbContext.TransactionHeaders
                .Include(t => t.Details)
                .FirstOrDefaultAsync(t => t.Id == transaction.RelatedTransactionId.Value);

            if (relatedOrder != null)
            {
                // Check if all items have been fully received
                bool allItemsReceived = await CheckAllOrderItemsReceivedAsync(relatedOrder.Id);

                // Update the purchase order status
                relatedOrder.Status = allItemsReceived ? "Completed" : "Partially Received";
                relatedOrder.UpdatedAt = DateTime.UtcNow;

                _dbContext.TransactionHeaders.Update(relatedOrder);
            }
        }

        await _dbContext.SaveChangesAsync();
    }

    public async Task RejectReceivingAsync(int id, string reason)
    {
        var transaction = await _dbContext.TransactionHeaders
            .FirstOrDefaultAsync(t => t.Id == id);

        if (transaction == null)
            throw new InvalidOperationException("Receiving not found.");

        if (transaction.Status != "Submitted")
            throw new InvalidOperationException("Receiving can only be rejected when in Submitted status.");

        transaction.Status = "Rejected";
        transaction.Notes = $"{transaction.Notes}\nRejection Reason: {reason}";
        transaction.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();
    }

    public async Task CancelReceivingAsync(int id, string? reason = null)
    {
        var transaction = await _dbContext.TransactionHeaders
            .FirstOrDefaultAsync(t => t.Id == id);

        if (transaction == null)
            throw new InvalidOperationException("Receiving not found.");

        if (transaction.Status == "Completed")
            throw new InvalidOperationException("Receiving cannot be cancelled after it has been completed.");

        transaction.Status = "Cancelled";
        transaction.Notes = string.IsNullOrEmpty(reason)
            ? transaction.Notes
            : $"{transaction.Notes}\nCancellation Reason: {reason}";
        transaction.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();
    }

    #endregion

    #region Credit Note Methods

    public async Task<TransactionHeaderDto> CreateCreditNoteAsync(CreateCreditNoteDto createCreditNoteDto, int userId)
    {


        // Removed dependency on TransactionProcess

        // Get the CreditNote stage type
        var creditNoteStage = await _dbContext.TransactionStageTypes
            .FirstOrDefaultAsync(tst => tst.Name == "CreditNote");
        if (creditNoteStage == null)
            throw new InvalidOperationException("CreditNote stage type not found.");

        // Create the transaction header
        var transaction = new TransactionHeader
        {
            TransactionNumber = await GenerateReferenceNumberAsync("CN"),
            ReferenceNumber = createCreditNoteDto.ReferenceNumber,

            // Removed ProcessId
            StageTypeId = creditNoteStage.Id,
            TransactionTypeId = 1, // Purchase transaction type
            SourceCostCenterId = createCreditNoteDto.SourceCostCenterId,
            SupplierId = createCreditNoteDto.SupplierId,
            TransactionDate = createCreditNoteDto.TransactionDate,
            Status = "Draft",
            Notes = $"{createCreditNoteDto.Notes}\nReason: {createCreditNoteDto.Reason}",
            RelatedTransactionId = createCreditNoteDto.RelatedTransactionId,
            CreatedById = userId,
            CreatedAt = DateTime.UtcNow,
            IsActive = true
        };

        // If related transaction is provided, get its reference number
        if (createCreditNoteDto.RelatedTransactionId.HasValue)
        {
            var relatedTransaction = await _dbContext.TransactionHeaders
                .FirstOrDefaultAsync(t => t.Id == createCreditNoteDto.RelatedTransactionId.Value);
            if (relatedTransaction != null)
            {
                transaction.RelatedTransactionNumber = relatedTransaction.TransactionNumber;
            }
        }

        _dbContext.TransactionHeaders.Add(transaction);
        await _dbContext.SaveChangesAsync();

        // Add details if provided
        if (createCreditNoteDto.Details != null && createCreditNoteDto.Details.Any())
        {
            foreach (var detailDto in createCreditNoteDto.Details)
            {
                var detail = new TransactionDetail
                {
                    TransactionId = transaction.Id,
                    ProductId = detailDto.ProductId,
                    BatchId = detailDto.BatchId,
                    UnitId = detailDto.UnitId ?? 1, // Default to first unit if not specified
                    Quantity = detailDto.Quantity,
                    UnitPrice = detailDto.UnitPrice ?? 0, // Default to 0 if not specified
                    TaxId = detailDto.TaxId,
                    Notes = detailDto.Notes,
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true
                };

                // Calculate tax amount and total amount
                if (detailDto.UnitPrice.HasValue)
                {
                    decimal lineTotal = detailDto.Quantity * detailDto.UnitPrice.Value;

                    // Apply tax if any
                    if (detailDto.TaxId.HasValue)
                    {
                        var tax = await _dbContext.Taxes.FindAsync(detailDto.TaxId.Value);
                        if (tax != null)
                        {
                            detail.TaxRate = tax.Rate;
                            decimal taxAmount = lineTotal * (tax.Rate / 100);
                            detail.TaxAmount = taxAmount;
                            lineTotal += taxAmount;
                        }
                    }

                    detail.LineTotal = lineTotal;
                }
                else
                {
                    detail.LineTotal = 0;
                }

                _dbContext.TransactionDetails.Add(detail);
            }

            await _dbContext.SaveChangesAsync();
            await UpdateTransactionTotalAsync(transaction.Id);
        }

        // Reload the transaction with all details
        return await GetTransactionByIdAsync(transaction.Id);
    }

    public async Task UpdateCreditNoteAsync(int id, UpdateCreditNoteDto updateCreditNoteDto)
    {
        var transaction = await _dbContext.TransactionHeaders
            .FirstOrDefaultAsync(t => t.Id == id);

        if (transaction == null)
            throw new InvalidOperationException("Credit note not found.");

        if (transaction.Status != "Draft")
            throw new InvalidOperationException("Credit note can only be updated when in Draft status.");

        transaction.SourceCostCenterId = updateCreditNoteDto.SourceCostCenterId;
        transaction.SupplierId = updateCreditNoteDto.SupplierId;
        transaction.TransactionDate = updateCreditNoteDto.TransactionDate;
        transaction.ReferenceNumber = updateCreditNoteDto.ReferenceNumber;
        transaction.Notes = $"{updateCreditNoteDto.Notes}\nReason: {updateCreditNoteDto.Reason}";
        transaction.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();
    }

    public async Task CompleteCreditNoteAsync(int id, int userId)
    {
        Console.WriteLine($"DEBUG Credit Note: CompleteCreditNoteAsync called for transaction {id}");

        try
        {
            var transaction = await _dbContext.TransactionHeaders
                .Include(t => t.Details)
                .FirstOrDefaultAsync(t => t.Id == id);

            if (transaction == null)
                throw new InvalidOperationException("Credit note not found.");

            if (transaction.Status != "Draft")
                throw new InvalidOperationException("Credit note can only be completed when in Draft status.");

            if (!transaction.SourceCostCenterId.HasValue)
                throw new InvalidOperationException("Credit note must have a source cost center to complete.");

        if (!transaction.Details.Any())
            throw new InvalidOperationException("Credit note must have at least one detail item to complete.");

        Console.WriteLine($"DEBUG Credit Note: Starting completion for transaction {transaction.Id} with {transaction.Details.Count} details");

        // Process each detail item to update stock
        foreach (var detail in transaction.Details)
        {
            if (detail.Quantity <= 0)
                continue;

            // Get current stock information to use average cost
            var currentStock = await _dbContext.StockOnHand
                .FirstOrDefaultAsync(s => s.ProductId == detail.ProductId &&
                                        s.CostCenterId == transaction.SourceCostCenterId.Value);

            if (currentStock == null || currentStock.Quantity < detail.Quantity)
                throw new InvalidOperationException($"Insufficient stock for product ID {detail.ProductId}. Available: {currentStock?.Quantity ?? 0}, Required: {detail.Quantity}");

            // Use the unit price from the credit note detail if provided, otherwise use current average cost
            var costToUse = currentStock.AverageCost ?? 0;
            var totalCostToDeduct = detail.Quantity * costToUse;

            Console.WriteLine($"DEBUG Credit Note: ProductId={detail.ProductId}, Quantity={detail.Quantity}, UnitPrice={detail.UnitPrice}, CurrentAvgCost={currentStock.AverageCost}, CostToUse={costToUse}, TotalCostToDeduct={totalCostToDeduct}");

            // Calculate base quantity if unit conversion is needed
            var baseQuantity = detail.Quantity; // Default to 1:1 conversion
            if (detail.UnitId > 0)
            {
                var unit = await _dbContext.Units.FindAsync(detail.UnitId);
                if (unit != null && unit.BaseConversionFactor > 0)
                {
                    baseQuantity = detail.Quantity * unit.BaseConversionFactor;
                }
            }

            // Update stock on hand - reduce quantities for credit note using weighted average logic
            var updateStockSql = @"
                UPDATE StockOnHand
                SET
                    Quantity = Quantity - @DeductQuantity,
                    BaseQuantity = ISNULL(BaseQuantity, 0) - @BaseQuantity,
                    AverageCost = CASE
                        WHEN (Quantity - @DeductQuantity) <= 0 THEN 0
                        ELSE (ISNULL(Quantity, 0) * ISNULL(AverageCost, 0) - @TotalCostToDeduct) / (Quantity - @DeductQuantity)
                    END,
                    LastUpdated = @LastUpdated
                WHERE ProductId = @ProductId AND CostCenterId = @CostCenterId";

            Console.WriteLine($"DEBUG Credit Note SQL: Executing stock update for ProductId={detail.ProductId}, CostCenterId={transaction.SourceCostCenterId.Value}, DeductQuantity={detail.Quantity}, TotalCostToDeduct={totalCostToDeduct}");

            var rowsAffected = await _dbContext.Database.ExecuteSqlRawAsync(updateStockSql,
                new Microsoft.Data.SqlClient.SqlParameter("@ProductId", detail.ProductId),
                new Microsoft.Data.SqlClient.SqlParameter("@CostCenterId", transaction.SourceCostCenterId.Value),
                new Microsoft.Data.SqlClient.SqlParameter("@DeductQuantity", detail.Quantity),
                new Microsoft.Data.SqlClient.SqlParameter("@BaseQuantity", baseQuantity),
                new Microsoft.Data.SqlClient.SqlParameter("@TotalCostToDeduct", totalCostToDeduct),
                new Microsoft.Data.SqlClient.SqlParameter("@LastUpdated", DateTime.UtcNow));

            Console.WriteLine($"DEBUG Credit Note SQL: Rows affected: {rowsAffected}");

            // Update product average cost across all cost centers
            var updateProductSql = @"
                UPDATE Product
                SET AverageCost = (
                    SELECT SUM(Quantity * ISNULL(AverageCost, 0)) / NULLIF(SUM(Quantity), 0)
                    FROM StockOnHand
                    WHERE ProductId = @ProductId AND Quantity > 0
                )
                WHERE ProductId = @ProductId";

            await _dbContext.Database.ExecuteSqlRawAsync(updateProductSql,
                new Microsoft.Data.SqlClient.SqlParameter("@ProductId", detail.ProductId));
        }

            // Update transaction status
            transaction.Status = "Completed";
            transaction.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();
            Console.WriteLine($"DEBUG Credit Note: Successfully completed transaction {transaction.Id}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"DEBUG Credit Note: Error completing transaction {id}: {ex.Message}");
            Console.WriteLine($"DEBUG Credit Note: Stack trace: {ex.StackTrace}");
            throw;
        }
    }

    public async Task CancelCreditNoteAsync(int id, string? reason = null)
    {
        var transaction = await _dbContext.TransactionHeaders
            .FirstOrDefaultAsync(t => t.Id == id);

        if (transaction == null)
            throw new InvalidOperationException("Credit note not found.");

        if (transaction.Status == "Completed")
            throw new InvalidOperationException("Credit note cannot be cancelled after it has been completed.");

        transaction.Status = "Cancelled";
        transaction.Notes = string.IsNullOrEmpty(reason)
            ? transaction.Notes
            : $"{transaction.Notes}\nCancellation Reason: {reason}";
        transaction.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();
    }

    #endregion

    #region Maintenance Methods

    public async Task RecalculateAllTransactionTotalsAsync()
    {
        // Get all transaction headers
        var transactions = await _dbContext.TransactionHeaders
            .Include(t => t.Details)
            .ToListAsync();

        foreach (var transaction in transactions)
        {
            // Recalculate line totals for each detail
            foreach (var detail in transaction.Details)
            {
                // Recalculate line total based on quantity and unit price
                decimal lineTotal = detail.Quantity * detail.UnitPrice;

                // Apply discount if any
                if ((detail.DiscountPercentage ?? 0m) > 0)
                {
                    decimal detailDiscountAmount = lineTotal * ((detail.DiscountPercentage ?? 0m) / 100m);
                    detail.DiscountAmount = detailDiscountAmount;
                    lineTotal -= detailDiscountAmount;
                }

                // Apply tax if any
                if (detail.TaxId.HasValue && (detail.TaxRate ?? 0m) > 0)
                {
                    decimal detailTaxAmount = lineTotal * ((detail.TaxRate ?? 0m) / 100m);
                    detail.TaxAmount = detailTaxAmount;
                    lineTotal += detailTaxAmount;
                }

                detail.LineTotal = lineTotal;
            }

            // Calculate subtotal (sum of line totals before tax)
            decimal subTotal = transaction.Details.Sum(d => d.LineTotal - (d.TaxAmount ?? 0));

            // Calculate tax amount
            decimal totalTaxAmount = transaction.Details.Sum(d => d.TaxAmount ?? 0);

            // Calculate discount amount
            decimal totalDiscountAmount = transaction.Details.Sum(d => d.DiscountAmount ?? 0);

            // Calculate total amount
            decimal totalAmount = transaction.Details.Sum(d => d.LineTotal);

            // Update the transaction
            transaction.SubTotal = subTotal;
            transaction.TaxAmount = totalTaxAmount;
            transaction.DiscountAmount = totalDiscountAmount;
            transaction.TotalAmount = totalAmount;
        }

        // Save all changes
        await _dbContext.SaveChangesAsync();
    }

    public async Task<TransactionHeaderDto> CreateCreditNoteFromReceivingAsync(int receivingTransactionId, CreateCreditNoteDto createCreditNoteDto, int userId)
    {
        // Get the receiving transaction with its details
        var receivingTransaction = await _dbContext.TransactionHeaders
            .Include(t => t.Details)
            .FirstOrDefaultAsync(t => t.Id == receivingTransactionId);

        if (receivingTransaction == null)
            throw new InvalidOperationException("Receiving transaction not found.");

        // Verify it's a receiving transaction
        var receivingStage = await _dbContext.TransactionStageTypes
            .FirstOrDefaultAsync(tst => tst.Name == "Receiving");

        if (receivingStage == null || receivingTransaction.StageTypeId != receivingStage.Id)
            throw new InvalidOperationException("The specified transaction is not a receiving transaction.");

        if (receivingTransaction.Status != "Completed")
            throw new InvalidOperationException("Credit notes can only be created from completed receiving transactions.");

        // Get the CreditNote stage type
        var creditNoteStage = await _dbContext.TransactionStageTypes
            .FirstOrDefaultAsync(tst => tst.Name == "CreditNote");
        if (creditNoteStage == null)
            throw new InvalidOperationException("CreditNote stage type not found.");

        // Create the credit note transaction header
        var transaction = new TransactionHeader
        {
            TransactionNumber = await GenerateReferenceNumberAsync("CN"),
            ReferenceNumber = createCreditNoteDto.ReferenceNumber,
            StageTypeId = creditNoteStage.Id,
            TransactionTypeId = 1, // Purchase transaction type
            SourceCostCenterId = receivingTransaction.SourceCostCenterId ?? createCreditNoteDto.SourceCostCenterId,
            SupplierId = receivingTransaction.SupplierId ?? createCreditNoteDto.SupplierId,
            TransactionDate = createCreditNoteDto.TransactionDate,
            Status = "Draft",
            Notes = $"{createCreditNoteDto.Notes}\nReason: {createCreditNoteDto.Reason}\nCreated from Receiving: {receivingTransaction.TransactionNumber}",
            RelatedTransactionId = receivingTransactionId,
            RelatedTransactionNumber = receivingTransaction.TransactionNumber,
            CreatedById = userId,
            CreatedAt = DateTime.UtcNow,
            IsActive = true
        };

        _dbContext.TransactionHeaders.Add(transaction);
        await _dbContext.SaveChangesAsync();

        // Create credit note details based on receiving transaction details
        // If specific details are provided in the DTO, use those; otherwise, use all receiving details
        var detailsToProcess = createCreditNoteDto.Details?.Any() == true
            ? createCreditNoteDto.Details
            : receivingTransaction.Details.Select(rd => new CreateCreditNoteDetailDto
            {
                ProductId = rd.ProductId,
                BatchId = rd.BatchId,
                UnitId = rd.UnitId,
                Quantity = rd.Quantity,
                UnitPrice = rd.UnitPrice,
                TaxId = rd.TaxId,
                Notes = rd.Notes,
                Reason = createCreditNoteDto.Reason
            }).ToList();

        foreach (var detailDto in detailsToProcess)
        {
            // Validate that the product exists in the original receiving transaction
            var originalDetail = receivingTransaction.Details
                .FirstOrDefault(rd => rd.ProductId == detailDto.ProductId);

            if (originalDetail == null)
                throw new InvalidOperationException($"Product ID {detailDto.ProductId} was not found in the original receiving transaction.");

            // Validate quantity doesn't exceed what was originally received
            if (detailDto.Quantity > originalDetail.Quantity)
                throw new InvalidOperationException($"Credit note quantity ({detailDto.Quantity}) cannot exceed originally received quantity ({originalDetail.Quantity}) for product ID {detailDto.ProductId}.");

            var detail = new TransactionDetail
            {
                TransactionId = transaction.Id,
                ProductId = detailDto.ProductId,
                BatchId = detailDto.BatchId,
                UnitId = detailDto.UnitId ?? originalDetail.UnitId,
                Quantity = detailDto.Quantity,
                UnitPrice = detailDto.UnitPrice ?? originalDetail.UnitPrice,
                TaxId = detailDto.TaxId ?? originalDetail.TaxId,
                Notes = detailDto.Notes,
                CreatedAt = DateTime.UtcNow,
                IsActive = true
            };

            // Calculate line total
            detail.LineTotal = detail.Quantity * detail.UnitPrice;

            _dbContext.TransactionDetails.Add(detail);
        }

        await _dbContext.SaveChangesAsync();
        await UpdateTransactionTotalAsync(transaction.Id);

        return await GetTransactionByIdAsync(transaction.Id);
    }

    #endregion

    #region Helper Methods

    private void SetNavigationPropertyNames(TransactionHeaderDto dto, TransactionHeader transaction)
    {
        if (transaction.StageType != null)
        {
            dto.StageTypeName = transaction.StageType.Name;
        }

        if (transaction.SourceCostCenter != null)
        {
            dto.SourceCostCenterName = transaction.SourceCostCenter.Name;
        }

        if (transaction.DestinationCostCenter != null)
        {
            dto.DestinationCostCenterName = transaction.DestinationCostCenter.Name;
        }

        if (transaction.Supplier != null)
        {
            dto.SupplierName = transaction.Supplier.Name;
        }
    }

    #endregion
}
